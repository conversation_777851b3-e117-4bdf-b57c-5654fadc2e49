package com.mandong.api.module.erp.service.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageRespVO;
import com.mandong.api.module.erp.controller.admin.role.vo.SdkRoleCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dal.sdkMysql.role.SdkRoleMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserDeviceMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserLoginLogsMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

/**
 * 游戏用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SdkRoleServiceImpl implements SdkRoleService {

    @Resource
    private SdkRoleMapper sdkRoleMapper;
    @Resource
    private SdkOptMapper sdkOptMapper;

    @Override
    public PageResult<RolePageRespVO> getPage(RolePageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        
        // 只有运营角色(163)需要特殊处理权限
        if (!roleId.equals("163")) {
            // 非运营人员直接查询
            Page<RolePageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
            IPage<RolePageRespVO> rolePageRespVOIPage = sdkRoleMapper.selectRolePageNew(page, pageReqVO, null);
            return new PageResult<>(rolePageRespVOIPage.getRecords(), rolePageRespVOIPage.getTotal());
        }
        
        // 运营人员权限处理逻辑
        List<SdkRoleCondition> conditions = new ArrayList<>();
        List<SdkOptLinkDO> optVOS = sdkOptMapper.selectLinks(userId);
        if (optVOS.isEmpty()) {
            // 无权限数据，返回空结果
            return new PageResult<>(new ArrayList<>(), 0L);
        }
        
        // 检查是否有查询条件（产品ID）
        List<Integer> requestProductIds = pageReqVO.getProductId();
        List<String> requestChannelCodes = pageReqVO.getChannelCode();
        boolean hasProductFilter = requestProductIds != null && !requestProductIds.isEmpty();
        boolean hasChannelFilter = requestChannelCodes != null && !requestChannelCodes.isEmpty();
        
        // 创建产品和渠道的权限映射，用于快速查找
        Map<Integer, Set<String>> authorizedProductChannels = new HashMap<>();
        for (SdkOptLinkDO opt : optVOS) {
            authorizedProductChannels
                .computeIfAbsent(opt.getProductId(), k -> new HashSet<>())
                .add(opt.getChannelCode());
        }
        
        if (hasProductFilter) {
            for (Integer productId : requestProductIds) {
                Set<String> authorizedChannels = authorizedProductChannels.get(productId);
                
                // 用户没有权限查询该产品
                if (authorizedChannels == null) {
                    // 添加所有有权限的产品和渠道
                    for (Map.Entry<Integer, Set<String>> entry : authorizedProductChannels.entrySet()) {
                        for (String channelCode : entry.getValue()) {
                            conditions.add(new SdkRoleCondition()
                                    .setProductId(entry.getKey())
                                    .setChannelCode(channelCode));
                        }
                    }
                    break; // 找到一个无权限的产品就跳出循环，添加所有权限
                }
                
                // 用户有权限查询该产品
                if (!hasChannelFilter) {
                    // 无渠道筛选，添加该产品所有授权渠道
                    for (String channel : authorizedChannels) {
                        conditions.add(new SdkRoleCondition()
                                .setProductId(productId)
                                .setChannelCode(channel));
                    }
                } else {
                    // 有渠道筛选，检查每个请求的渠道是否有权限
                    boolean addedAny = false;
                    for (String requestChannel : requestChannelCodes) {
                        if (authorizedChannels.contains(requestChannel)) {
                            conditions.add(new SdkRoleCondition()
                                    .setProductId(productId)
                                    .setChannelCode(requestChannel));
                            addedAny = true;
                        }
                    }
                    
                    // 如果所有请求的渠道都没权限，添加该产品所有有权限的渠道
                    if (!addedAny) {
                        for (String channel : authorizedChannels) {
                            conditions.add(new SdkRoleCondition()
                                    .setProductId(productId)
                                    .setChannelCode(channel));
                        }
                    }
                }
            }
        } else {
            // 无产品筛选条件，添加所有有权限的产品和渠道
            for (Map.Entry<Integer, Set<String>> entry : authorizedProductChannels.entrySet()) {
                for (String channel : entry.getValue()) {
                    conditions.add(new SdkRoleCondition()
                            .setProductId(entry.getKey())
                            .setChannelCode(channel));
                }
            }
        }
        
        // 清空原有筛选条件，使用我们构建的conditions
        pageReqVO.setProductId(null);
        pageReqVO.setChannelCode(null);
        
        // 执行查询
        Page<RolePageRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<RolePageRespVO> rolePageRespVOIPage = sdkRoleMapper.selectRolePageNew(page, pageReqVO, conditions);
        return new PageResult<>(rolePageRespVOIPage.getRecords(), rolePageRespVOIPage.getTotal());
    }

}
