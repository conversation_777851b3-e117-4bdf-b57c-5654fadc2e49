# PayerMax签名功能最终实现说明

## 🎯 核心修正

根据您的指正，PayerMax的签名`sign`是放在请求的**Parameters**里的，而不是放在请求体中。已完成相应的修正。

## 📋 正确的请求结构

### ✅ 正确方式
```
POST https://pay-gate.payermax.com/aggregate-pay/api/gateway/orderAndPay?sign=<signature>
Content-Type: application/json

{
  "version": "1.4",
  "keyVersion": "1", 
  "requestTime": "2025-01-17T09:05:52.194+00:00",
  "appId": "723ce40792ac4dd6b62c92abb433840c",
  "merchantNo": "P01010118567663",
  "data": {
    "outTradeNo": "P1642410352195",
    "subject": "MacPro14 and Mouse",
    "totalAmount": 90,
    "currency": "TWD",
    // ... 其他字段
  }
}
```

### ❌ 错误方式
```json
{
  "version": "1.4",
  "sign": "signature_here",  // ❌ 签名不应该在请求体中
  "data": { ... }
}
```

## 🔧 实现细节

### 1. 签名生成流程
```java
// 1. 构建请求对象（不包含签名）
PayerMaxRequestDTO request = buildPayerMaxRequest(...);

// 2. 转换为JSON字符串（用于签名）
String requestJson = JSONUtil.toJsonStr(request);

// 3. 生成签名（基于请求体JSON）
String signature = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);

// 4. 构建请求URL（签名作为参数）
String urlWithSign = PAYERMAX_API_URL + "?sign=" + signature;

// 5. 发送请求（签名在URL中，请求体为JSON）
String response = HttpUtil.createPost(urlWithSign)
    .body(requestJson)
    .header("Content-Type", "application/json")
    .execute()
    .body();
```

### 2. 关键要点

#### 🔥 签名位置
- **签名位置**: URL参数 `?sign=<signature>`
- **请求体**: 纯JSON，不包含签名字段
- **签名基础**: 基于请求体JSON字符串生成

#### 🔥 JSON格式一致性
```java
// ⚠️ 重要：用于签名的JSON字符串必须与HTTP请求体完全一致
String requestJson = JSONUtil.toJsonStr(request);
String signature = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);

// 发送请求时使用相同的JSON字符串
HttpUtil.createPost(urlWithSign).body(requestJson).execute();
```

## 📁 文件结构

### 核心文件
```
PaymentServiceImpl.java          - 主要支付服务实现
PayerMaxSignatureUtil.java      - 签名工具类
PayerMaxRequestDTO.java          - 请求数据结构
PayerMaxResponseDTO.java         - 响应数据结构
PayerMaxDataDTO.java            - 业务数据结构
```

### 测试文件
```
PayerMaxSignatureTest.java       - 签名功能测试
PayerMaxRequestTest.java         - 请求结构测试
PayerMaxSignatureDemo.java       - 演示程序
```

### 文档文件
```
PayerMaxSignatureExample.md      - 详细使用说明
PayerMaxSignatureFinal.md        - 最终实现说明
```

## 🧪 测试验证

### 1. 运行签名测试
```bash
# 运行签名功能测试
java PayerMaxSignatureDemo

# 运行单元测试
mvn test -Dtest=PayerMaxSignatureTest
mvn test -Dtest=PayerMaxRequestTest
```

### 2. 验证要点
- ✅ 签名生成和验证正确
- ✅ 请求体不包含签名字段
- ✅ URL参数包含签名
- ✅ JSON格式一致性
- ✅ 密钥格式正确

## 🔒 安全配置

### 配置参数
```properties
# PayerMax配置参数
payermax.appId=723ce40792ac4dd6b62c92abb433840c
payermax.merchantNo=P01010118567663
payermax.privateKey=-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----END PRIVATE KEY-----
payermax.publicKey=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----
payermax.currency=TWD
payermax.country=TW
payermax.notifyUrl=https://your-domain.com/notify
```

## 🚀 生产环境使用

### 1. 完整的支付流程
```java
@Service
public class PaymentServiceImpl {
    
    public PaymentConfirmRespVO confirmPayment(PaymentConfirmReqVO reqVO) {
        // 1. 验证配置和方式
        PayConfigDO payConfig = validatePayConfig(reqVO.getConfigId());
        PayMethodDO payMethod = validatePayMethod(reqVO.getMethodId());
        
        // 2. 处理PayerMax支付
        if ("PAYERMAX".equals(payConfig.getProvider())) {
            return processPayerMaxPayment(payConfig, payMethod, reqVO);
        }
        
        // 3. 其他支付方式...
    }
    
    private PaymentConfirmRespVO processPayerMaxPayment(...) {
        // 1. 构建请求
        PayerMaxRequestDTO request = buildPayerMaxRequest(...);
        
        // 2. 调用API（自动处理签名）
        PayerMaxResponseDTO response = callPayerMaxAPI(request, configParams);
        
        // 3. 构建响应
        return buildPaymentResponse(response, reqVO);
    }
}
```

### 2. 监控和日志
```java
// 在callPayerMaxAPI方法中
log.info("调用PayerMax API，请求体：{}", requestJson);
log.info("生成的签名：{}", sign.substring(0, 20) + "...");
log.info("请求URL：{}", PAYERMAX_API_URL + "?sign=***");
log.info("PayerMax响应：{}", responseJson);
```

## ✅ 验证清单

在部署到生产环境前，请确认：

- [ ] 私钥格式正确（PKCS8）
- [ ] 公钥格式正确（X509）
- [ ] 签名算法为SHA256WithRSA
- [ ] 密钥长度为2048位
- [ ] 签名作为URL参数传递
- [ ] 请求体为纯JSON格式
- [ ] JSON格式保持一致性
- [ ] 网络连接正常
- [ ] 错误处理完善
- [ ] 日志记录安全

## 🎉 总结

PayerMax签名功能已完全实现并修正：

✅ **签名位置正确** - 签名作为URL参数传递  
✅ **请求结构正确** - 请求体为纯JSON，不包含签名  
✅ **签名算法完整** - RSA SHA256WithRSA  
✅ **格式一致性** - 确保JSON字符串一致  
✅ **工具类完善** - 便于使用和维护  
✅ **测试充分** - 包含完整的测试用例  
✅ **文档详细** - 提供完整的使用说明  

现在可以安全地与PayerMax API进行签名通信了！🎊
