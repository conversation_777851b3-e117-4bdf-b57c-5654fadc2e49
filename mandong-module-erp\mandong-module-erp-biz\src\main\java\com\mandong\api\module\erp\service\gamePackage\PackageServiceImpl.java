package com.mandong.api.module.erp.service.gamePackage;

import cn.hutool.core.date.DateTime;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.game.vo.GameVersionRespVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackageAddReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackagePageReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackResultRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskResultDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.SdkPackTaskMapper;
import com.mandong.api.module.erp.dal.sdkMysql.game.SdkPackTaskResultMapper;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.text.UnicodeUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.PRODUCT_NOT_EXISTS;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.TASK_NOT_EXISTS;

@Service
public class PackageServiceImpl implements PackageService {

    @Resource
    SdkPackTaskMapper sdkPackTaskMapper;
    @Resource
    SdkProductMapper sdkProductMapper;
    @Resource
    SdkPackTaskResultMapper sdkPackTaskResultMapper;

    @Override
    public PageResult<GameVersionRespVO> getPage(PackagePageReqVO reqVO) {

        return sdkPackTaskMapper.getPackagePage(reqVO);
    }

    @Override
    public Long addPackage(PackageAddReqVO packageAddReqVO) {

        SdkProductDO sdkProductDO = sdkProductMapper.selectById(packageAddReqVO.getProductId());
        if (sdkProductDO == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }
        String gameName = getGameName(getShortName(sdkProductDO.getProductName()));
        String savePath = "_{$versionNo}_{$channelCode}_{$time}.apk";
        if (sdkProductDO.getPlatform() == 2) {
            savePath = "_{$versionNo}_{$channelCode}_{$time}.ipa";
        }

        SdkPackTaskDO sdkPackTaskDO = new SdkPackTaskDO();
        BeanUtils.copyProperties(packageAddReqVO, sdkPackTaskDO);
        sdkPackTaskDO.setCpsList(UnicodeUtil.toUnicode(packageAddReqVO.getCpsList()));
        sdkPackTaskDO.setRunProcess(1);
        sdkPackTaskDO.setManageUid(2);
        sdkPackTaskDO.setSavePackName(gameName + savePath);
        sdkPackTaskDO.setCreateTime(new DateTime().toTimestamp().getTime()/1000);
        sdkPackTaskMapper.insert(sdkPackTaskDO);
        return  0L;
    }

    @Override
    public Integer deletePackage(Long id) {
        SdkPackTaskDO sdkPackTaskDO = sdkPackTaskMapper.selectById(id);
        if (sdkPackTaskDO == null) {
            throw exception(TASK_NOT_EXISTS);
        }
        sdkPackTaskDO.setStatus(2);
        return sdkPackTaskMapper.updateById(sdkPackTaskDO);
    }

    @Override
    public List<SdkPackResultRespVO> getResult(Long id) {

        return sdkPackTaskResultMapper.getResult(id);
    }


    /**
     * 获取游戏名字的拼音首字母
     */
    public static String getGameName(String zh) {
        if (zh == null || zh.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < zh.length(); i++) {
            char c = zh.charAt(i);

            if (isChinese(c)) {
                // 使用HuTool的PinyinUtil获取完整拼音，然后取首字母
                String pinyin = PinyinUtil.getPinyin(String.valueOf(c));
                if (pinyin != null && !pinyin.isEmpty()) {
                    result.append(pinyin.charAt(0)).append("");
                }
            } else {
                // 非中文字符原样保留
                result.append(c);
            }
        }

        return result.toString().toUpperCase();
    }


    /**
     * 判断字符是否为中文
     */
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A;
    }


    public static String getShortName(String name) {
        String shortName = name;

        // 检查是否存在中文()
        Pattern pattern1 = Pattern.compile("（(.*)）");
        Matcher matcher1 = pattern1.matcher(name);
        while (matcher1.find()) {
            shortName = shortName.replace(matcher1.group(), "");
        }

        // 总有些傻逼括号都打不对
        // 修复正则表达式，原来的写法有语法错误
        Pattern pattern2 = Pattern.compile("（(.*)\\)");  // 修改了这里，使用转义字符
        Matcher matcher2 = pattern2.matcher(name);
        while (matcher2.find()) {
            shortName = shortName.replace(matcher2.group(), "");
        }

        Pattern pattern3 = Pattern.compile("\\((.*)）");
        Matcher matcher3 = pattern3.matcher(name);
        while (matcher3.find()) {
            shortName = shortName.replace(matcher3.group(), "");
        }

        // 英文括号
        Pattern pattern4 = Pattern.compile("\\((.*)\\)");
        Matcher matcher4 = pattern4.matcher(name);
        while (matcher4.find()) {
            shortName = shortName.replace(matcher4.group(), "");
        }

        shortName = shortName.replace(":", "：");

        if (shortName.contains("-")) {
            String[] nameArr = shortName.split("-");
            shortName = nameArr[0];
        }

        String[] replaceStrs = {"官方手游", "手游", "®", "专服", "_android", "_ios",
                "安卓", "越狱", "苹果", "正版", "单机版", "单机",
                "_appstore", "_", "-", "联运"};

        for (String str : replaceStrs) {
            shortName = shortName.toLowerCase().replace(str, "");
        }

        return shortName;
    }



}
