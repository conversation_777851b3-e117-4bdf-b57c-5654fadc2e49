package com.mandong.api.module.erp.framework.security.config;

import com.mandong.api.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * ERP 模块的 Security 配置
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false, value = "erpSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("erpAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // 支付确认接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/confirm-payment")).permitAll();
                
                // 支付配置查询接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/config/list")).permitAll();
                
                // PayerMax回调接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/notify/**")).permitAll();
                
                // 其他支付相关的公开接口
                registry.requestMatchers(buildAppApi("/pay/public/**")).permitAll();
            }

        };
    }

}
