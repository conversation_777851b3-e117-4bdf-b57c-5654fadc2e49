package com.mandong.api.module.erp.controller.admin.order.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 运营人员的权限配置 VO
 * 
 * 用于表示运营人员对哪些产品和渠道有权限
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class SdkOperationPermissionVO {
    
    /**
     * 产品ID
     */
    private Long productId;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 渠道代码
     */
    private String channelCode;
    
    /**
     * 渠道名称
     */
    private String channelName;
} 