package com.mandong.api.module.erp.dal.dataobject.opt;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;

@TableName("qsdk_opt_link")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkOptLinkDO {
    /**
     *
     */
    private Integer id;
    /**
     * 关联关系表id
     */
    private Integer linkId;
    /**
     * 产品id
     */
    private Integer productId;
    private String productName;

    private String channelCode;
    private String channelName;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

}