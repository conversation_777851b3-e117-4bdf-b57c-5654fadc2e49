package com.mandong.api.module.erp.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 订单交易统计 Request VO")
@Data
@ToString(callSuper = true)
public class SdkOrderSummaryReqVO {

    @Schema(description = "交易时间")
    private Long[] payTime;

    @Schema(description = "游戏id", example = "12")
    private List<Long> productId;

    @Schema(description = "区服名称")
    private String serverName;
    
    @Schema(description = "区服列表")
    private List<String> servers;

    private List<String> channelCode;

    @Schema(description = "订单来源（1--龙成后台  2---sdk后台)")
    @NotNull
    private Integer orderSource;

}
