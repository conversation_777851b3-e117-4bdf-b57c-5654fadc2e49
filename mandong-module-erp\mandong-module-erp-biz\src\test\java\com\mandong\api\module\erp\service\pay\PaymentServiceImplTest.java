package com.mandong.api.module.erp.service.pay;

import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmRespVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigMethodDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigParamDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PaymentServiceImpl 测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PaymentServiceImplTest {

    @Mock
    private PayConfigService payConfigService;

    @Mock
    private PayMethodService payMethodService;

    @InjectMocks
    private PaymentServiceImpl paymentService;

    private PaymentConfirmReqVO reqVO;
    private PayConfigDO payConfig;
    private PayMethodDO payMethod;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        reqVO = new PaymentConfirmReqVO();
        reqVO.setConfigId(1L);
        reqVO.setMethodId(1L);
        reqVO.setAmount(new BigDecimal("100.00"));
        reqVO.setCurrency("TWD");
        reqVO.setProductName("测试商品");
        reqVO.setUserId("user123");
        reqVO.setReturnUrl("https://example.com/return");

        // 准备支付配置
        payConfig = new PayConfigDO();
        payConfig.setId(1L);
        payConfig.setName("PayerMax支付");
        payConfig.setProvider("PAYERMAX");
        payConfig.setStatus(false); // 启用状态

        // 准备配置参数
        List<PayConfigParamDO> params = Arrays.asList(
            createParam("appId", "test_app_id"),
            createParam("merchantNo", "test_merchant"),
            createParam("currency", "TWD"),
            createParam("country", "TW"),
            createParam("notifyUrl", "https://example.com/notify")
        );
        payConfig.setParams(params);

        // 准备支付方式关联
        List<PayConfigMethodDO> methods = Arrays.asList(
            createMethod(1L, true)
        );
        payConfig.setMethods(methods);

        // 准备支付方式
        payMethod = new PayMethodDO();
        payMethod.setId(1L);
        payMethod.setCode("KAKAOPAY");
        payMethod.setName("KaKao Pay");
        payMethod.setProvider("PAYERMAX");
        payMethod.setStatus(true);
    }

    private PayConfigParamDO createParam(String key, String value) {
        PayConfigParamDO param = new PayConfigParamDO();
        param.setParamKey(key);
        param.setParamValue(value);
        return param;
    }

    private PayConfigMethodDO createMethod(Long methodId, Boolean status) {
        PayConfigMethodDO method = new PayConfigMethodDO();
        method.setMethodId(methodId);
        method.setStatus(status);
        return method;
    }

    @Test
    void testConfirmPayment_PayerMax_Success() {
        // Mock 服务调用
        when(payConfigService.getPayConfig(1L)).thenReturn(payConfig);
        when(payConfigService.getPayConfigWithDetails(1L)).thenReturn(payConfig);
        when(payMethodService.getPayMethod(1L)).thenReturn(payMethod);

        // 执行测试
        PaymentConfirmRespVO response = paymentService.confirmPayment(reqVO);

        // 验证结果
        assertNotNull(response);
        assertEquals("PAYERMAX", response.getPaymentMethod());
        assertEquals("PENDING", response.getStatus());
        assertNotNull(response.getPaymentUrl());
        assertNotNull(response.getPaymentOrderNo());
        assertNotNull(response.getThirdPartyOrderNo());
        assertNotNull(response.getExpireTime());
        
        assertTrue(response.getPaymentUrl().contains("pay-gate.payermax.com"));
        assertTrue(response.getThirdPartyOrderNo().startsWith("PAYERMAX_"));
    }

    @Test
    void testConfirmPayment_GooglePay_Success() {
        // 修改为Google Pay
        payConfig.setProvider("GOOGLE_PAY");
        payMethod.setProvider("GOOGLE_PAY");

        // Mock 服务调用
        when(payConfigService.getPayConfig(1L)).thenReturn(payConfig);
        when(payConfigService.getPayConfigWithDetails(1L)).thenReturn(payConfig);
        when(payMethodService.getPayMethod(1L)).thenReturn(payMethod);

        // 执行测试
        PaymentConfirmRespVO response = paymentService.confirmPayment(reqVO);

        // 验证结果
        assertNotNull(response);
        assertEquals("GOOGLE_PAY", response.getPaymentMethod());
        assertEquals("PENDING", response.getStatus());
        assertNotNull(response.getPaymentUrl());
        assertTrue(response.getPaymentUrl().contains("pay.google.com"));
        assertTrue(response.getThirdPartyOrderNo().startsWith("GOOGLE_"));
    }
}
