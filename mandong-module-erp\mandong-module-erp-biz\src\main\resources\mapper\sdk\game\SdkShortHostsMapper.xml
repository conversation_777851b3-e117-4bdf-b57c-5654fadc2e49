<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.game.SdkShortHostsMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO">
    <!--@mbg.generated-->
    <!--@Table qsdk_short_hosts-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manageUid" jdbcType="INTEGER" property="manageUid" />
    <result column="host" jdbcType="VARCHAR" property="host" />
    <result column="hasIcp" jdbcType="BOOLEAN" property="hasIcp" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, manageUid, `host`, hasIcp, note
  </sql>
</mapper>