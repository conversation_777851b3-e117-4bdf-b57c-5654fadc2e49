package com.mandong.api.module.erp.controller.admin.pay;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.*;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import com.mandong.api.module.erp.service.pay.PayMethodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点管理-支付方式")
@RestController
@RequestMapping("/site/pay-method")
@Validated
public class PayMethodController {

    @Resource
    private PayMethodService payMethodService;

    @GetMapping("/list")
    @Operation(summary = "获得支付方式列表")
    public CommonResult<List<PayMethodVO>> getPayMethodList(@Valid PayMethodListReqVO listReqVO) {
        List<PayMethodDO> list = payMethodService.getPayMethodList(listReqVO);
        return success(BeanUtils.toBean(list, PayMethodVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得支付方式")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:pay-method:query')")
    public CommonResult<PayMethodVO> getPayMethod(@RequestParam("id") Long id) {
        PayMethodDO payMethod = payMethodService.getPayMethod(id);
        return success(BeanUtils.toBean(payMethod, PayMethodVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得支付方式分页")
    @PreAuthorize("@ss.hasPermission('erp:pay-method:query')")
    public CommonResult<PageResult<PayMethodVO>> getPayMethodPage(@Valid PayMethodPageReqVO pageReqVO) {
        PageResult<PayMethodDO> pageResult = payMethodService.getPayMethodPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PayMethodVO.class));
    }

    @PostMapping("/create")
    @Operation(summary = "创建支付方式")
    @PreAuthorize("@ss.hasPermission('erp:pay-method:create')")
    public CommonResult<Long> createPayMethod(@Valid @RequestBody PayMethodSaveReqVO createReqVO) {
        return success(payMethodService.createPayMethod(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新支付方式")
    @PreAuthorize("@ss.hasPermission('erp:pay-method:update')")
    public CommonResult<Boolean> updatePayMethod(@Valid @RequestBody PayMethodSaveReqVO updateReqVO) {
        payMethodService.updatePayMethod(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @Operation(summary = "删除支付方式")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('erp:pay-method:delete')")
    public CommonResult<Boolean> deletePayMethod(@RequestParam("id") Long id) {
        payMethodService.deletePayMethod(id);
        return success(true);
    }

}
