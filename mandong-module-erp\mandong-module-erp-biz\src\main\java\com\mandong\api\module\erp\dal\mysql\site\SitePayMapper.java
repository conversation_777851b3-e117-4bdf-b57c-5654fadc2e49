package com.mandong.api.module.erp.dal.mysql.site;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.site.vo.*;
import com.mandong.api.module.erp.dal.dataobject.site.SitePay;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayI18n;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SitePayMapper extends BaseMapperX<SitePay> {

    default PageResult<SitePayPageRespVO> getPage(SitePayPageReqVO pageReqVO){
        return selectJoinPage(pageReqVO, SitePayPageRespVO.class, new MPJLambdaWrapperX<SitePay>()
                .selectAll(SitePay.class)
                .likeIfPresent(SitePay::getName,pageReqVO.getName())
                .orderByDesc(SitePay::getId)

        );
    }

    default List<SitePayPageRespVO> getGamePayList(){
        return selectJoinList( SitePayPageRespVO.class, new MPJLambdaWrapperX<SitePay>()
                .selectAll(SitePay.class)
                .selectAll(SitePayI18n.class)
                .leftJoin(SitePayI18n.class,SitePayI18n::getSiteId,SitePay::getId)
                .selectCollection(SitePayI18n.class, SitePayPageRespVO::getI18ns)
                .orderByDesc(SitePay::getId)

        );
    }

    default SitePayDetailRespVO get(Long id){
        return selectJoinOne(SitePayDetailRespVO.class,new MPJLambdaWrapperX<SitePay>()
                .eqIfPresent(SitePay::getId,id)
                .select(SitePayI18n::getCategory,SitePayI18n::getLocale,SitePayI18n::getDescription,SitePayI18n::getTitle)
                .leftJoin(SitePayI18n.class,SitePayI18n::getSiteId,SitePay::getId)



        );
    }

    @Select("SELECT " +
            "    g.id, " +
            "    g.icon_url AS iconUrl, " +
            "    g.banner_url AS bannerUrl, " +
            "    g.rating, " +
            "    g.player_count AS playerCount, " +
            "    g.product_id as productId, " +
            "    g.product_code as productCode, " +
            "    g.server_url as serverUrl, " +

            "    CAST( " +
            "        JSON_OBJECT( " +
            "            'title', JSON_OBJECT( " +
            "                'zh', i18n_zh.title, " +
            "                'en', i18n_en.title, " +
            "                'ko', i18n_ko.title " +
            "            ), " +
            "            'description', JSON_OBJECT( " +
            "                'zh', i18n_zh.description, " +
            "                'en', i18n_en.description, " +
            "                'ko', i18n_ko.description " +
            "            ), " +
            "            'category', JSON_OBJECT( " +
            "                'zh', i18n_zh.category, " +
            "                'en', i18n_en.category, " +
            "                'ko', i18n_ko.category " +
            "            ) " +
            "        ) " +
            "    AS CHAR) AS i18n, " +
            "    CAST( " +
            "        (SELECT JSON_ARRAYAGG( " +
            "            JSON_OBJECT( " +
            "                'id', p.id, " +
            "                'imageUrl', p.img_url, " +
            "                'skuCode', p.sku_code, " +
            "                'price', p.price, " +
            "                'originalPrice', p.price, " +
            "                'currency', p.currency, " +
            "                'i18n', JSON_OBJECT( " +
            "                    'title', JSON_OBJECT( " +
            "                        'zh', sku_i18n_zh.title, " +
            "                        'en', sku_i18n_en.title, " +
            "                        'ko', sku_i18n_ko.title " +
            "                    ), " +
            "                    'description', JSON_OBJECT( " +
            "                        'zh', sku_i18n_zh.description, " +
            "                        'en', sku_i18n_en.description, " +
            "                        'ko', sku_i18n_ko.description " +
            "                    ) " +
            "                ) " +
            "            ) " +
            "        ) " +
            "        FROM site_pay_sku p " +
            "        LEFT JOIN site_pay_sku_i18n sku_i18n_zh ON p.id = sku_i18n_zh.sku_id AND sku_i18n_zh.locale = 'zh' " +
            "        LEFT JOIN site_pay_sku_i18n sku_i18n_en ON p.id = sku_i18n_en.sku_id AND sku_i18n_en.locale = 'en' " +
            "        LEFT JOIN site_pay_sku_i18n sku_i18n_ko ON p.id = sku_i18n_ko.sku_id AND sku_i18n_ko.locale = 'ko' " +
            "        WHERE p.site_id = g.id) " +
            "    AS CHAR) AS products " +
            "FROM site_pay g " +
            "LEFT JOIN site_pay_i18n i18n_zh ON g.id = i18n_zh.site_id AND i18n_zh.locale = 'zh' " +
            "LEFT JOIN site_pay_i18n i18n_en ON g.id = i18n_en.site_id AND i18n_en.locale = 'en' " +
            "LEFT JOIN site_pay_i18n i18n_ko ON g.id = i18n_ko.site_id AND i18n_ko.locale = 'ko'" +
            " WHERE g.id =#{id} "
    )
    SitePayInfoRespVO getInfo(@Param("id") Long id);

}