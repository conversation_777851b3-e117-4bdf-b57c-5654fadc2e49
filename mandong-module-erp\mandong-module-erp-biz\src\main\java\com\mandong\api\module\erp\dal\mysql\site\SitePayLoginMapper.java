package com.mandong.api.module.erp.dal.mysql.site;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.site.vo.SitePayLoginVO;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLogin;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLoginConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SitePayLoginMapper extends BaseMapperX<SitePayLogin> {

    default  SitePayLoginVO  get(Long id){
        return selectJoinList(SitePayLoginVO.class,new MPJLambdaWrapperX<SitePayLogin>()
                .selectAll(SitePayLogin.class)
                .eqIfPresent(SitePayLogin::getId, id)
                .select(SitePayLoginConfig::getClientId)
                .leftJoin(SitePayLoginConfig.class,on->
                   on.eq(SitePayLoginConfig::getSitePayId,SitePayLogin::getSitePayId).eq(SitePayLoginConfig::getType,SitePayLogin::getType)
                 )
        ) .stream().findFirst().orElse(null);
    }

}