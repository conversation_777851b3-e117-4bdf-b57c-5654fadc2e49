package com.mandong.api.module.erp.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - Google登录 Request VO")
@Data
public class GameGoogleLoginReqVO {

    @Schema(description = "Google OAuth授权码", requiredMode = Schema.RequiredMode.REQUIRED, example = "4/0AX4XfWjYZ...")
    @NotEmpty(message = "Google授权码不能为空")
    private String code;

    @Schema(description = "游戏ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "game123")
    @NotEmpty(message = "游戏ID不能为空")
    private String gameId;

    @Schema(description = "产品ID", example = "product123")
    private String productId;

}