<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mandong.api</groupId>
  <artifactId>mandong-dependencies</artifactId>
  <version>2.4.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>mandong-dependencies</name>
  <description>基础 bom 文件，管理整个项目的依赖版本</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <properties>
    <tika-core.version>2.9.2</tika-core.version>
    <podam.version>8.0.2.RELEASE</podam.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
    <mapstruct.version>1.6.3</mapstruct.version>
    <fastjson.version>1.2.83</fastjson.version>
    <weixin-java.version>4.6.0</weixin-java.version>
    <mybatis.version>3.5.17</mybatis.version>
    <opentracing.version>0.33.0</opentracing.version>
    <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
    <commons-compress.version>1.27.1</commons-compress.version>
    <rocketmq-spring.version>2.3.1</rocketmq-spring.version>
    <ip2region.version>2.7.0</ip2region.version>
    <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
    <redisson.version>3.41.0</redisson.version>
    <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
    <mqtt.version>1.2.5</mqtt.version>
    <spring.boot.version>3.4.1</spring.boot.version>
    <springdoc.version>2.7.0</springdoc.version>
    <commons-io.version>2.17.0</commons-io.version>
    <lock4j.version>2.2.7</lock4j.version>
    <hutool-6.version>6.0.0-M19</hutool-6.version>
    <jsoup.version>1.18.3</jsoup.version>
    <mybatis-plus.version>3.5.9</mybatis-plus.version>
    <knife4j.version>4.6.0</knife4j.version>
    <lombok.version>1.18.36</lombok.version>
    <flowable.version>7.0.1</flowable.version>
    <skywalking.version>9.0.0</skywalking.version>
    <mockito-inline.version>5.2.0</mockito-inline.version>
    <justauth.version>2.0.5</justauth.version>
    <velocity.version>2.4.1</velocity.version>
    <captcha-plus.version>2.0.3</captcha-plus.version>
    <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
    <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
    <hutool-5.version>5.8.35</hutool-5.version>
    <revision>2.4.1-SNAPSHOT</revision>
    <jsch.version>0.1.55</jsch.version>
    <spring-boot-admin.version>3.4.1</spring-boot-admin.version>
    <netty.version>4.1.116.Final</netty.version>
    <jedis-mock.version>1.1.8</jedis-mock.version>
    <guava.version>33.4.0-jre</guava.version>
    <aws-java-sdk-s3.version>1.12.777</aws-java-sdk-s3.version>
    <commons-net.version>3.11.1</commons-net.version>
    <jimureport.version>1.8.1</jimureport.version>
    <easyexcel.verion>4.0.3</easyexcel.verion>
    <druid.version>1.2.24</druid.version>
    <easy-trans.version>3.0.6</easy-trans.version>
    <dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${netty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.github.mouzt</groupId>
        <artifactId>bizlog-sdk</artifactId>
        <version>${bizlog-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-biz-tenant</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-biz-data-permission</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-biz-ip</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-web</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-security</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-websocket</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xingfudeshi</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-mybatis</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-3-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-spring-boot-starter</artifactId>
        <version>${easy-trans.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-mybatis-plus-extend</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-anno</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-redis</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dm8.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.opengauss</groupId>
        <artifactId>opengauss-jdbc</artifactId>
        <version>${opengauss.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.kingbase</groupId>
        <artifactId>kingbase8</artifactId>
        <version>${kingbase.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-job</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-mq</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-protection</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-monitor</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-logback-1.x</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-opentracing</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-api</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-util</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-noop</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
        <exclusions>
          <exclusion>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-server-cloud</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-test</artifactId>
        <version>${revision}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito-inline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.fppt</groupId>
        <artifactId>jedis-mock</artifactId>
        <version>${jedis-mock.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.co.jemos.podam</groupId>
        <artifactId>podam</artifactId>
        <version>${podam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-process</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-common</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-spring-boot-starter-excel</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-jdk8</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool-5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.hutool</groupId>
        <artifactId>hutool-extra</artifactId>
        <version>${hutool-6.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.verion}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${commons-compress.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tika</groupId>
        <artifactId>tika-core</artifactId>
        <version>${tika-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>${jsch.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-captcha-plus</artifactId>
        <version>${captcha-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-s3</artifactId>
        <version>${aws-java-sdk-s3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-justauth</artifactId>
        <version>${justauth.version}</version>
        <exclusions>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-pay</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimureport-spring-boot3-starter-fastjson2</artifactId>
        <version>${jimureport.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.eclipse.paho</groupId>
        <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        <version>${mqtt.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
