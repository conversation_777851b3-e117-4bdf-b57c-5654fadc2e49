package com.mandong.api.module.erp.dal.dataobject.channel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mandong.api.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_channel")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkChannelDO   {

    @TableId
    private Long id;
    /**
     * 渠道编码
     */
    @TableField("channelCode")
    private String channelCode;
    /**
     * 渠道名
     */
    @TableField("channelName")
    private String channelName;
    /**
     *  游戏id
     */
    @TableField("productId")
    private Long productId;
    @TableField("createTime")
    private Long createTime;

    /**
     * 游戏名
     */
//    @TableField("productName")
//    private String productName;


}
