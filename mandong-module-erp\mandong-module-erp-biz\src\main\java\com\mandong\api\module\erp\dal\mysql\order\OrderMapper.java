package com.mandong.api.module.erp.dal.mysql.order;

import java.util.*;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.order.OrderDO;
import org.apache.ibatis.annotations.Mapper;
import com.mandong.api.module.erp.controller.admin.order.vo.*;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderMapper extends BaseMapperX<OrderDO> {

    default PageResult<OrderDO> selectPage(OrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderDO>()
               /* .likeIfPresent(OrderDO::getUserName, reqVO.getUserName())
                .eqIfPresent(OrderDO::getUserAccount, reqVO.getUserAccount())
                .eqIfPresent(OrderDO::getDepartment, reqVO.getDepartment())
                .eqIfPresent(OrderDO::getPromotion, reqVO.getPromotion())
                .likeIfPresent(OrderDO::getGameName, reqVO.getGameName())
                .eqIfPresent(OrderDO::getUserId, reqVO.getUserId())
                .eqIfPresent(OrderDO::getService, reqVO.getService())
                .likeIfPresent(OrderDO::getRoleName, reqVO.getRoleName())
                .eqIfPresent(OrderDO::getAmountUsd, reqVO.getAmountUsd())
                .eqIfPresent(OrderDO::getAmount, reqVO.getAmount())
                .eqIfPresent(OrderDO::getGoods, reqVO.getGoods())
                .eqIfPresent(OrderDO::getPayment, reqVO.getPayment())
                .betweenIfPresent(OrderDO::getPayTime, reqVO.getPayTime())
                .eqIfPresent(OrderDO::getTransactionNumber, reqVO.getTransactionNumber())
                .eqIfPresent(OrderDO::getOrderNumber, reqVO.getOrderNumber())
                .eqIfPresent(OrderDO::getTeamBelong, reqVO.getTeamBelong())
                .betweenIfPresent(OrderDO::getRegistrationTime, reqVO.getRegistrationTime())
                .eqIfPresent(OrderDO::getOrderStatus, reqVO.getOrderStatus())
                .eqIfPresent(OrderDO::getGameOrderNumber, reqVO.getGameOrderNumber())
                .betweenIfPresent(OrderDO::getCompletionTime, reqVO.getCompletionTime())
                .eqIfPresent(OrderDO::getUserIp, reqVO.getUserIp())
                .eqIfPresent(OrderDO::getOrderSource, reqVO.getOrderSource())
                .betweenIfPresent(OrderDO::getCreateTime, reqVO.getCreateTime())*/
                .orderByDesc(OrderDO::getId));
    }

}