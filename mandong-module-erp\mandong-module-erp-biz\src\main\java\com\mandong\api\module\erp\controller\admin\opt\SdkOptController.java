package com.mandong.api.module.erp.controller.admin.opt;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.opt.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.service.opt.SdkOptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.error;
import static com.mandong.api.framework.common.pojo.CommonResult.success;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LEAD_GROUP_LINKS_EXISTS;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.OPT_LINKS_EXISTS;

@Tag(name = "管理后台 - SDK带队归属")
@RestController
@RequestMapping("/erp/opt")
@Validated
public class SdkOptController {

    @Resource
    private SdkOptService sdkOptService;


    @GetMapping("/page")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasAnyPermissions('erp:opt:query','erp:leadGroup:query')")
    public CommonResult<PageResult<OptRespVO>> getPage(@Valid OptPageReqVO pageReqVO) {

        return success(sdkOptService.getOptPage(pageReqVO));
    }

    @GetMapping("/LinkPage")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:opt:query')")
    public CommonResult<PageResult<OptLinksPageRespVO>> getLinkPage(@Valid OptLinksPageReqVO pageReqVO) {

        return success(sdkOptService.getOptLinksPage(pageReqVO));
    }

    @GetMapping("/monthlyStats")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:opt:query')")
    public CommonResult<List<OptMonthlyStatsVO>> getOptMonthlyStats(@Valid Long id ,Long  startTime,Long  endTime) {

        return success(sdkOptService.getOptMonthlyStats(id,startTime,endTime));
    }

    @PostMapping("/add")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:opt:add')")
    public CommonResult<Integer> add(@Valid @RequestBody OptAddReqVO pageReqVO) {

        return success(sdkOptService.add(pageReqVO));
    }
    @PostMapping("/createGroupLinks")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:opt:add')")
    public CommonResult<Integer> addOptLink(@Valid @RequestBody OptLinkAddReqVO pageReqVO) {

        return success(sdkOptService.createGroupLinks(pageReqVO));
    }

    @GetMapping("/deleteGroupLinks")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:opt:delete')")
    public CommonResult<Integer> deleteGroupLinks(@RequestParam("id") Long id) {


        return success(sdkOptService.deleteGroupLinks(id));
    }


    @GetMapping("/delete")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:opt:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {
        List<SdkOptLinkDO> leadGroupLinks = sdkOptService.getOptLinks(id);
        if (!leadGroupLinks.isEmpty()) {
            return error(OPT_LINKS_EXISTS);
        }

        return success(sdkOptService.delete(id));
    }



}
