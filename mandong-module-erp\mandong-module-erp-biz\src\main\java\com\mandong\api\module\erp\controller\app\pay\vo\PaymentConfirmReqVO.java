package com.mandong.api.module.erp.controller.app.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "支付页面 - 支付确认 Request VO")
@Data
public class PaymentConfirmReqVO {

    @Schema(description = "支付配置编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "支付配置编号不能为空")
    private Long configId;

    @Schema(description = "支付方式编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "支付方式编号不能为空")
    private Long methodId;


    @Schema(description = "网站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "网站id不能为空")
    private Long siteId;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    @Schema(description = "货币", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    @NotNull(message = "货币不能为空")
    private String currency;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "游戏充值")
    @NotNull(message = "商品名称不能为空")
    private String productName;

    @Schema(description = "商品Sku", requiredMode = Schema.RequiredMode.REQUIRED, example = "游戏充值")
    @NotNull(message = "商品Sku不能为空")
    private String skuCode;



    @Schema(description = "用户ID", example = "12345")
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "角色ID", example = "12345")
    @NotNull(message = "角色ID不能为空")
    private String roleId;

    @Schema(description = "区服id", example = "12345")
    @NotNull(message = "区服ID不能为空")
    private String serverId;

    @Schema(description = "角色名称", example = "12345")
    @NotNull(message = "角色名称不能为空")
    private String roleName;




    @Schema(description = "返回地址", example = "https://example.com/return")
    private String returnUrl;


}
