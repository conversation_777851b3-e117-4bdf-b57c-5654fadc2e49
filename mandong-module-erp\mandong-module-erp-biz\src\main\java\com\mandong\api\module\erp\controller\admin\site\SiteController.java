package com.mandong.api.module.erp.controller.admin.site;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.site.vo.*;
import com.mandong.api.module.erp.dal.dataobject.site.SitePay;
import com.mandong.api.module.erp.service.site.SitePayService;
import com.mandong.api.module.erp.util.I18nHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点管理-支付网站管理")
@RestController
@RequestMapping("/site/paySite")
@Validated
public class SiteController {

    @Resource
    SitePayService sitePayService;



    @GetMapping("/page")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<PageResult<SitePayPageRespVO>> getPage(@Valid SitePayPageReqVO pageReqVO) {

        return success(sitePayService.getPage(pageReqVO));
    }

    @GetMapping("/getGamePayList")
    @Operation(summary = "获得分页")
    @PermitAll
    public CommonResult<List<SitePayPageRespVO>> getPage() {

        return success(sitePayService.getGamePayList());
    }


    @PostMapping("/add")
    @Operation(summary = "新增支付网站")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:add')")
    public CommonResult<Integer> add(@Valid @RequestBody SitePayAddReqVO reqVO) {

        return success(sitePayService.add(reqVO));
    }

    @GetMapping("/delete")
    @Operation(summary = "删除支付网站")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {

        return success(sitePayService.delete(id));
    }

    @GetMapping("/get")
    @Operation(summary = "获取支付网站")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<SitePayDetailRespVO> get(@RequestParam("id") Long id) {
        return success(sitePayService.get(id));
    }

    @GetMapping("/getInfo")
    @Operation(summary = "获取支付网站信息（包含多语言和产品列表）")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<SitePayInfoRespVO> getInfo(@RequestParam("id") Long id) {
        return success(sitePayService.getInfo(id));
    }



    @PostMapping("/edit")
    @Operation(summary = "修改支付网站")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:edit')")
    public CommonResult<Integer> edit(@Valid @RequestBody SitePayAddReqVO pageReqVO) {
        return success(sitePayService.edit(pageReqVO));
    }

    @GetMapping("/getSiteInfo")
    @Operation(summary = "获取网站信息")
    @PermitAll
    public CommonResult<SitePayInfoRespVO> getSiteInfo(@RequestParam("id") Long id) {
        return success(sitePayService.getInfo(id));
    }

    @PostMapping("/addSku")
    @Operation(summary = "新增支付网站商品")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:add')")
    public CommonResult<Integer> addSku(@Valid @RequestBody List<SitePayAddSkuReqVO> reqVO) {

        return success(sitePayService.addSku(reqVO));
    }

    @GetMapping("/getSkuList")
    @Operation(summary = "获取支付网站商品")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<List<SitePaySkuRespVO>> getSkuList(@RequestParam("id") Long id) {

        return success(sitePayService.getSkuList(id));
    }

    @GetMapping("/deleteSku")
    @Operation(summary = "删除支付网站商品")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:delete')")
    public CommonResult<Integer> deleteSku(@RequestParam("id") Long id) {

        return success(sitePayService.deleteSku(id));
    }

    @PostMapping("/updateSku")
    @Operation(summary = "更新支付网站商品")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:add')")
    public CommonResult<Integer> updateSku(@Valid @RequestBody SitePayAddSkuReqVO reqVO) {

        return success(sitePayService.updateSku(reqVO));
    }

    @PostMapping("/deleteSkuBatch")
    @Operation(summary = "删除支付网站商品")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:delete')")
    public CommonResult<Integer> deleteSku(@RequestBody List<Long> id) {

        return success(sitePayService.deleteSku(id));
    }

    @GetMapping("/getI18nOnly")
    @Operation(summary = "仅获取指定网站的多语言信息")
    @PermitAll
    public CommonResult<JSONObject> getI18nOnly(@RequestParam("id") Long id, @RequestParam(value = "lang", required = false) String lang) {
        SitePayInfoRespVO siteInfo = sitePayService.getInfo(id);
        
        if (siteInfo == null || siteInfo.getI18nObj() == null) {
            return success(null);
        }
        
        // 如果指定了语言，只返回该语言的信息
        if (lang != null && !lang.isEmpty()) {
            JSONObject result = new JSONObject();
            JSONObject i18nObj = siteInfo.getI18nObj();
            
            // 遍历所有字段（如title、description、category）
            for (String field : i18nObj.keySet()) {
                JSONObject fieldObj = i18nObj.getJSONObject(field);
                if (fieldObj != null && fieldObj.containsKey(lang)) {
                    result.put(field, fieldObj.getString(lang));
                }
            }
            
            return success(result);
        }
        
        // 如果没有指定语言，返回完整的多语言信息
        return success(siteInfo.getI18nObj());
    }
    
    @GetMapping("/getProductsOnly")
    @Operation(summary = "仅获取指定网站的产品信息")
    @PermitAll
    public CommonResult<JSONArray> getProductsOnly(@RequestParam("id") Long id) {
        SitePayInfoRespVO siteInfo = sitePayService.getInfo(id);
        
        if (siteInfo == null || siteInfo.getProductsArray() == null) {
            return success(null);
        }
        
        return success(siteInfo.getProductsArray());
    }



}
