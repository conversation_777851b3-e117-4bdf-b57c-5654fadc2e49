package com.mandong.api.module.erp.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mandong.api.framework.common.util.date.DateUtils;
import com.mandong.api.framework.excel.core.annotations.DictFormat;
import com.mandong.api.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Schema(description = "管理后台 - Sdk订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkOrderExportRespVo {

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String orderNo;
    @ExcelProperty("运营人员")
    private String optName;

    @ExcelProperty("渠道名称")
    private String channelName;

    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    private String username;
    /**
     * 角色名
     */
    @ExcelProperty("角色名")
    private String roleName;
    /**
     * 角色id
     */
    @ExcelProperty("角色id")
    private String roleId;
    /**
     * 角色等级
     */
    @ExcelProperty("角色等级")
    private String roleLevel;
    /**
     * 区服
     */
    @ExcelProperty("区服")
    private String serverName;
    /**
     * 金额
     */
    @ExcelProperty("金额")
    private float dealAmount;
    /**
     * 游戏名称
     */
    @ExcelProperty("游戏名")
    private String productName;
    /**
     * 付款时间
     */
    @ExcelProperty(value = "付款时间")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    public void setPayTime(Long timestamp) {
        if (timestamp != null && timestamp > 0) {
            this.payTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
        } else {
            this.payTime = null;
        }
    }

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    public void setCreateTime(Long timestamp) {
        if (timestamp != null && timestamp > 0) {
            this.createTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
        } else {
            this.createTime = null;
        }
    }

    /**
     * 付款状态
     */
    @ExcelProperty(value = "付款状态",converter = DictConvert.class)
    @DictFormat("erp_order_status")
    private String payStatus;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payName;


}
