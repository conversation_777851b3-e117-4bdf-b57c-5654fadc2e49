package com.mandong.api.module.erp.service.pay;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodListReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodPageReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodSaveReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 支付方式 Service 接口
 *
 * <AUTHOR>
 */
public interface PayMethodService {

    /**
     * 创建支付方式
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPayMethod(@Valid PayMethodSaveReqVO createReqVO);

    /**
     * 更新支付方式
     *
     * @param updateReqVO 更新信息
     */
    void updatePayMethod(@Valid PayMethodSaveReqVO updateReqVO);

    /**
     * 删除支付方式
     *
     * @param id 编号
     */
    void deletePayMethod(Long id);

    /**
     * 获得支付方式
     *
     * @param id 编号
     * @return 支付方式
     */
    PayMethodDO getPayMethod(Long id);

    /**
     * 获得支付方式列表
     *
     * @param reqVO 查询条件
     * @return 支付方式列表
     */
    List<PayMethodDO> getPayMethodList(PayMethodListReqVO reqVO);

    /**
     * 获得支付方式分页
     *
     * @param pageReqVO 分页查询
     * @return 支付方式分页
     */
    PageResult<PayMethodDO> getPayMethodPage(PayMethodPageReqVO pageReqVO);

}
