package com.mandong.api.module.erp.dal.mysql.pay;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodListReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodPageReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支付方式 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayMethodMapper extends BaseMapperX<PayMethodDO> {

    default PageResult<PayMethodDO> selectPage(PayMethodPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PayMethodDO>()
                .eqIfPresent(PayMethodDO::getProvider, reqVO.getProvider())
                .eqIfPresent(PayMethodDO::getStatus, reqVO.getStatus())
                .orderByAsc(PayMethodDO::getSort));
    }

    default List<PayMethodDO> selectList(PayMethodListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PayMethodDO>()
                .eqIfPresent(PayMethodDO::getProvider, reqVO.getProvider())
                .orderByAsc(PayMethodDO::getSort));
    }

}