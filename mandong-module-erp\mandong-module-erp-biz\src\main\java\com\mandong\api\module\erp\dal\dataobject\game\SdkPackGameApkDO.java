package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@TableName("qsdk_pack_gameapk")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkPackGameApkDO {

    @TableId
    private Integer id;


    @TableField("icon")
    private String icon;

    @TableField("appName")
    private String appName;

    @TableField("initImage")
    private String initImage;

    @TableField("productId")
    private Integer productId;

    @TableField("versionNo")
    private Integer versionNo;

    @TableField("versionName")
    private String versionName;

    @TableField("note")
    private String note;

    @TableField("apkUrl")
    private String apkUrl;

    @TableField("updateTime")
    private Integer updateTime;

    @TableField("size")
    private String size;

    @TableField("package")
    private String Package;

    /**
    * 1执行成功 2等待 3正在执行 4失败
    */
    @TableField("status")
    private Boolean status;

    /**
    * 1 使用Quick
    */
    @TableField("useQuick")
    private Boolean useQuick;

    @TableField("messageTips")
    private String messageTips;

    @TableField("taskInfo")
    private String taskInfo;
}