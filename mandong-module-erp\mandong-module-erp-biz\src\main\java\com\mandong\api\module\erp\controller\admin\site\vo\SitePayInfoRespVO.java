package com.mandong.api.module.erp.controller.admin.site.vo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayI18n;
import com.mandong.api.module.erp.dal.dataobject.site.SitePaySkuI18n;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SitePayInfoRespVO {

    private Long id;

    private String iconUrl;

    private String productId;

    private String productCode;
    private String serverUrl;

    private String bannerUrl;

    private Float rating;

    private String playerCount;
    private String currency;

    // 原始i18n字段，JSON字符串
    private String i18n;

    // 解析后的i18n对象，直接使用JSONObject更简单
    private JSONObject i18nObj;

    // 原始products字段，JSON字符串
    private String products;
    
    // 解析后的products对象，直接使用JSONArray更简单
    private JSONArray productsArray;
    
    // 存储解析后的产品列表，不会序列化到JSON中
    private transient List<ProductVO> productList;
}
