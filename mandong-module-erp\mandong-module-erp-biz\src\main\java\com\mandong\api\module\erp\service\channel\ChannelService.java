package com.mandong.api.module.erp.service.channel;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.ChannelGetListRespVO;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.SdkChannelVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 渠道 service
 */
public interface ChannelService {
    PageResult<SdkChannelVO> getChannel(ChannelGetListRespVO channelGetListRespVO);
    SdkChannelDO getChannel(Long id);
    Integer deleteChannel(Long id);
    PageResult<SdkChannelVO> getChannelPage(ChannelGetListRespVO channelGetListRespVO);
    Long addChannel(SdkChannelVO sdkChannelVO);
    Long editChannel(SdkChannelVO sdkChannelVO);
}
