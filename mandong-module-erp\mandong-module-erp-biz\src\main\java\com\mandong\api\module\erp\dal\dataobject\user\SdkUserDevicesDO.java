package com.mandong.api.module.erp.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@TableName("qsdk_user_devices")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkUserDevicesDO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户id
     */
    @TableField("uid")
    private Integer uid;

    /**
     * 设备id
     */
    @TableField("deviceId")
    private String deviceId;

    /**
     * 激活时间
     */
    @TableField("activeTime")
    private Long activeTime;

    /**
     * 产品id
     */
    @TableField("productId")
    private Integer productId;
}