<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.site.SitePayMapper">
    <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.site.SitePay">
        <!--@mbg.generated-->
        <!--@Table site_pay-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="banner_url" jdbcType="VARCHAR" property="bannerUrl"/>
        <result column="rating" jdbcType="DECIMAL" property="rating"/>
        <result column="player_count" jdbcType="VARCHAR" property="playerCount"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="call_back_url" jdbcType="VARCHAR" property="callBackUrl"/>
        <result column="server_url" jdbcType="VARCHAR" property="serverUrl"/>
        <result column="login_url" jdbcType="VARCHAR" property="loginUrl"/>
        <result column="pay_url" jdbcType="VARCHAR" property="payUrl"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        `name`,
        product_id,
        url,
        icon_url,
        banner_url,
        rating,
        player_count,
        `status`,
        call_back_url,
        server_url,
        login_url,
        pay_url,
        creator,
        create_time,
        updater,
        update_time,
        deleted,
        tenant_id
    </sql>



</mapper>