# 支付服务优化说明

## 概述

本次优化完善了支付确认接口，特别是PayerMax支付的实现，构建了完整的PayerMax API请求参数结构，并提供了可扩展的支付提供商架构。

## 主要改进

### 1. 完整的PayerMax DTO结构

创建了完整的PayerMax API请求和响应DTO类：

- `PayerMaxRequestDTO` - 主请求对象
- `PayerMaxDataDTO` - 业务数据对象
- `PayerMaxPaymentDetailDTO` - 支付详情
- `PayerMaxEnvInfoDTO` - 环境信息
- `PayerMaxGoodsDetailDTO` - 商品详情
- `PayerMaxShippingInfoDTO` - 收货信息
- `PayerMaxBillingInfoDTO` - 账单信息
- `PayerMaxRiskParamsDTO` - 风险参数
- `PayerMaxResponseDTO` - 响应对象
- `PayerMaxResponseDataDTO` - 响应数据

### 2. 优化的支付流程

```java
public PaymentConfirmRespVO confirmPayment(PaymentConfirmReqVO reqVO) {
    // 1. 验证支付配置
    PayConfigDO payConfig = validatePayConfig(reqVO.getConfigId());
    
    // 2. 验证支付方式
    PayMethodDO payMethod = validatePayMethod(reqVO.getMethodId());
    
    // 3. 验证支付配置和支付方式的关联关系
    validatePayConfigMethod(payConfig, reqVO.getMethodId());
    
    // 4. 执行业务验证
    validateBusinessRules(reqVO);
    
    // 5. 根据支付提供商调用相应的支付接口
    return processPayment(payConfig, payMethod, reqVO);
}
```

### 3. PayerMax支付处理

```java
private PaymentConfirmRespVO processPayerMaxPayment(PayConfigDO payConfig, PayMethodDO payMethod, PaymentConfirmReqVO reqVO) {
    try {
        // 1. 获取PayerMax的配置参数
        Map<String, String> configParams = getPayerMaxConfigParams(payConfig);
        
        // 2. 构建PayerMax请求参数
        PayerMaxRequestDTO payerMaxRequest = buildPayerMaxRequest(configParams, payMethod, reqVO);
        
        // 3. 调用PayerMax API
        PayerMaxResponseDTO payerMaxResponse = callPayerMaxAPI(payerMaxRequest, configParams);
        
        // 4. 构建响应
        return buildPaymentResponse(payerMaxResponse, reqVO);
        
    } catch (Exception e) {
        log.error("PayerMax支付处理失败", e);
        throw exception(PAYMENT_ORDER_INVALID);
    }
}
```

## 接口使用说明

### 支付确认接口

**接口地址**: `POST /app-api/pay/config/confirm-payment`

**权限要求**: 无需权限

**请求参数**:
```json
{
    "configId": 1,
    "methodId": 1,
    "amount": 100.00,
    "currency": "TWD",
    "productName": "游戏充值",
    "skuCode": "SKU001",
    "userId": "user123",
    "roleId": "role456",
    "serverId": "server789",
    "roleName": "角色名称",
    "returnUrl": "https://example.com/return"
}
```

**响应参数**:
```json
{
    "code": 0,
    "msg": "成功",
    "data": {
        "paymentUrl": "https://pay-gate.payermax.com/pay?token=xxx",
        "paymentMethod": "PAYERMAX",
        "paymentOrderNo": "PAY_20231201120000_1234",
        "thirdPartyOrderNo": "PAYERMAX_1701417600000",
        "status": "PENDING",
        "expireTime": "2023-12-01T12:30:00"
    }
}
```

## 配置说明

### PayerMax配置参数

在支付配置中需要设置以下参数：

| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| appId | PayerMax应用ID | 723ce40792ac4dd6b62c92abb433840c |
| merchantNo | 商户号 | P01010118567663 |
| currency | 默认货币 | TWD |
| country | 默认国家 | TW |
| notifyUrl | 异步通知地址 | https://example.com/notify |
| privateKey | 商户私钥 | (用于签名) |
| publicKey | PayerMax公钥 | (用于验签) |

## 扩展支持

### 添加新的支付提供商

1. 在 `processPayment` 方法中添加新的case
2. 实现对应的处理方法
3. 创建相应的DTO类
4. 添加配置参数支持

```java
switch (provider.toUpperCase()) {
    case "PAYERMAX":
        return processPayerMaxPayment(payConfig, payMethod, reqVO);
    case "GOOGLE_PAY":
        return processGooglePayPayment(payConfig, payMethod, reqVO);
    case "NEW_PROVIDER":
        return processNewProviderPayment(payConfig, payMethod, reqVO);
    default:
        throw exception(PAYMENT_PROVIDER_NOT_SUPPORTED);
}
```

## 测试

提供了完整的单元测试 `PaymentServiceImplTest`，覆盖了主要的支付流程。

运行测试：
```bash
mvn test -Dtest=PaymentServiceImplTest
```

## 注意事项

1. 当前PayerMax API调用是模拟实现，实际使用时需要：
   - 实现真实的HTTP请求
   - 添加签名生成和验证
   - 处理异常情况和重试机制

2. 业务验证方法 `validateBusinessRules` 需要根据实际业务需求实现

3. 建议添加支付日志记录和监控

4. 生产环境需要配置真实的PayerMax API地址和证书
