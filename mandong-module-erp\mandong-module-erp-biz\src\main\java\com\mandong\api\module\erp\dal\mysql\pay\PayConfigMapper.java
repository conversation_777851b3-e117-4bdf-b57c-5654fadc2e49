package com.mandong.api.module.erp.dal.mysql.pay;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigPageReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支付配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayConfigMapper extends BaseMapperX<PayConfigDO> {

    default PageResult<PayConfigDO> selectPage(PayConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PayConfigDO>()
                .likeIfPresent(PayConfigDO::getName, reqVO.getName())
                .eqIfPresent(PayConfigDO::getProvider, reqVO.getProvider())
                .eqIfPresent(PayConfigDO::getStatus, reqVO.getStatus())
                .orderByDesc(PayConfigDO::getId));
    }

    default List<PayConfigDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<PayConfigDO>()
                .eq(PayConfigDO::getStatus, status)
                .orderByAsc(PayConfigDO::getSort));
    }

    default List<PayConfigDO> selectListByStatus(Boolean status) {
        return selectList(new LambdaQueryWrapperX<PayConfigDO>()
                .eq(PayConfigDO::getStatus, status)
                .orderByAsc(PayConfigDO::getSort));
    }

}