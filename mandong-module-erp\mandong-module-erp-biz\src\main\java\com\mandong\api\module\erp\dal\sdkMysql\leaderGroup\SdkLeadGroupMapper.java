package com.mandong.api.module.erp.dal.sdkMysql.leaderGroup;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupPageReqVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupRespVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupVO;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserPageReqVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserPageRespVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupDO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.role.SdkRoleDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkLeadGroupMapper extends BaseMapperX<SdkLeadGroupDO> {




    default PageResult<LeadGroupRespVO> selectPage(LeadGroupPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, LeadGroupRespVO.class, new MPJLambdaWrapperX<SdkLeadGroupDO>()
                .eqIfPresent(SdkLeadGroupDO::getUserName, pageReqVO.getUserName())
                .eqIfPresent(SdkLeadGroupDO::getGroupName, pageReqVO.getGroupName())
                .eqIfPresent(SdkLeadGroupDO::getUid, pageReqVO.getUid())
                .orderByDesc(SdkLeadGroupDO::getCreateTime)
        );
    }

    default List<LeadGroupVO> selectPage(Long uid) {
        return selectJoinList(LeadGroupVO.class, new MPJLambdaWrapperX<SdkLeadGroupDO>()
                .eqIfPresent(SdkLeadGroupDO::getUid, uid)
                .selectAll(SdkLeadGroupDO.class)
                .selectCollection(SdkLeadGroupLinkDO.class, LeadGroupVO::getLinks)
                .leftJoin(SdkLeadGroupLinkDO.class, on -> on.eq(SdkLeadGroupLinkDO::getLinkId, SdkLeadGroupDO::getId))
                .orderByDesc(SdkLeadGroupDO::getCreateTime)
        );
    }

}