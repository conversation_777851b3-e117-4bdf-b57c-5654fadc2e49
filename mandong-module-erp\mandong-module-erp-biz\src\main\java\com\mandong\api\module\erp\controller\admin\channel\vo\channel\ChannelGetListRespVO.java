package com.mandong.api.module.erp.controller.admin.channel.vo.channel;

import com.mandong.api.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - ERP 渠道分类列表 Request VO")
@Data
public class ChannelGetListRespVO extends PageParam {
    private List<String> productId;
    private String channelName;
}
