package com.mandong.api.module.erp.controller.admin.leadGroup;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.service.leadGroup.SdkLeadGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.error;
import static com.mandong.api.framework.common.pojo.CommonResult.success;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LEAD_GROUP_LINKS_EXISTS;

@Tag(name = "管理后台 - SDK带队归属")
@RestController
@RequestMapping("/erp/leadGroup")
@Validated
public class SdkLeadGroupController {

    @Resource
    private SdkLeadGroupService sdkLeadGroupService;


    @GetMapping("/page")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<PageResult<LeadGroupRespVO>> getPage(@Valid LeadGroupPageReqVO pageReqVO) {

        return success(sdkLeadGroupService.getLeadGroupPage(pageReqVO));
    }

    @GetMapping("/LinkPage")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<PageResult<LeadGroupLinksPageRespVO>> getLinkPage(@Valid LeadGroupLinksPageReqVO pageReqVO) {

        return success(sdkLeadGroupService.getLeadGroupLinksPage(pageReqVO));
    }

    @GetMapping("/monthlyStats")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<List<LeadGroupMonthlyStatsVO>> getLeadGroupMonthlyStats(@Valid Long id,Long  startTime,Long  endTime ) {

        return success(sdkLeadGroupService.getLeadGroupMonthlyStats(id,startTime,endTime));
    }

    @PostMapping("/add")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:add')")
    public CommonResult<Integer> add(@Valid @RequestBody LeadGroupAddReqVO pageReqVO) {

        return success(sdkLeadGroupService.add(pageReqVO));
    }
    @PostMapping("/createGroupLinks")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:add')")
    public CommonResult<Integer> addLeadGroupLink(@Valid @RequestBody LeadGroupLinkAddReqVO pageReqVO) {

        return success(sdkLeadGroupService.createGroupLinks(pageReqVO));
    }

    @GetMapping("/deleteGroupLinks")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:delete')")
    public CommonResult<Integer> deleteGroupLinks(@RequestParam("id") Long id) {


        return success(sdkLeadGroupService.deleteGroupLinks(id));
    }


    @GetMapping("/delete")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {
        List<SdkLeadGroupLinkDO> leadGroupLinks = sdkLeadGroupService.getLeadGroupLinks(id);
        if (!leadGroupLinks.isEmpty()) {
            return error(LEAD_GROUP_LINKS_EXISTS);
        }

        return success(sdkLeadGroupService.delete(id));
    }



}
