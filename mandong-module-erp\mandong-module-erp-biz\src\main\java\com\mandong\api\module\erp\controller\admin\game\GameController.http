### Google登录接口测试

### 1. Google登录 - 成功案例
POST {{baseUrl}}/erp/game/google-login
Content-Type: application/json

{
  "code": "4/0AX4XfWjYZ1234567890abcdef",
  "gameId": "game123",
  "productId": "product123"
}

### 2. Google登录 - 缺少必填参数
POST {{baseUrl}}/erp/game/google-login
Content-Type: application/json

{
  "gameId": "game123"
}

### 3. Google登录 - 无效授权码
POST {{baseUrl}}/erp/game/google-login
Content-Type: application/json

{
  "code": "invalid_code",
  "gameId": "game123",
  "productId": "product123"
}

### 4. 普通登录接口（对比测试）
POST {{baseUrl}}/erp/game/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "testpass",
  "productId": "123"
}

### 变量定义
# @baseUrl = http://localhost:48080/admin-api
# 在实际使用时，请替换为正确的服务器地址
