package com.mandong.api.module.erp.service.site;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.site.vo.*;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayI18n;
import com.mandong.api.module.erp.dal.dataobject.site.SitePaySku;
import com.mandong.api.module.erp.dal.dataobject.site.SitePaySkuI18n;
import com.mandong.api.module.erp.dal.mysql.site.SitePayI18nMapper;
import com.mandong.api.module.erp.dal.mysql.site.SitePaySkuI18nMapper;
import com.mandong.api.module.erp.dal.mysql.site.SitePaySkuMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.mandong.api.module.erp.dal.dataobject.site.SitePay;
import com.mandong.api.module.erp.dal.mysql.site.SitePayMapper;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mandong.api.module.erp.controller.admin.site.vo.ProductVO;
import com.mandong.api.module.erp.util.JsonParseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.mandong.api.module.erp.util.I18nHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SitePayServiceImpl  implements SitePayService{
    @Resource
    private SitePayMapper sitePayMapper;
    @Resource
    private SitePayI18nMapper sitePayI18nMapper;

    @Resource
    private SitePaySkuMapper sitePaySkuMapper;

    @Resource
    private SitePaySkuI18nMapper sitePaySkuI18nMapper;

    private static final Logger logger = LoggerFactory.getLogger(SitePayServiceImpl.class);

    @Override
    public PageResult<SitePayPageRespVO> getPage(SitePayPageReqVO pageReqVO) {
        return sitePayMapper.getPage(pageReqVO);
    }

    @Override
    public List<SitePayPageRespVO> getGamePayList() {
        return sitePayMapper.getGamePayList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(SitePayAddReqVO reqVO) {
        SitePay sitePay = BeanUtils.toBean(reqVO, SitePay.class);
        sitePayMapper.insert(sitePay);

        List<SitePayI18nVO> i18ns = reqVO.getI18ns();
        List<SitePayI18n> i18nList = BeanUtils.toBean(i18ns, SitePayI18n.class);
        for (SitePayI18n i18n : i18nList) {
            i18n.setSiteId(  sitePay.getId());
        }

        sitePayI18nMapper.insertBatch(i18nList);



        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer delete(Long id) {
        sitePayI18nMapper.deleteById(id);
        sitePayMapper.deleteById(id);
        return 0;
    }

    @Override
    public SitePayDetailRespVO get(Long id) {
        SitePay sitePay = sitePayMapper.selectById(id);
        SitePayDetailRespVO respVO = BeanUtils.toBean(sitePay, SitePayDetailRespVO.class);
        List<SitePayI18n> sitePayI18ns = sitePayI18nMapper.selectList(SitePayI18n::getSiteId, id);
        respVO.setI18ns(sitePayI18ns);

        return respVO;
    }

    @Override
    public List<SitePaySkuRespVO> getSkuList(Long id) {



        return sitePaySkuMapper.getSkuList(id);
    }

    @Override
    public SitePayInfoRespVO getInfo(Long id) {
        SitePayInfoRespVO sitePayInfo = sitePayMapper.getInfo(id);
        
        // 如果返回的i18n和products不为null，进行JSON解析
        if (sitePayInfo != null) {
            // 处理i18n字段 - 直接使用Fastjson解析
            if (sitePayInfo.getI18n() != null) {
                try {
                    // 直接将JSON字符串解析为JSONObject对象
                    JSONObject i18nObj = JSON.parseObject(sitePayInfo.getI18n());
                    sitePayInfo.setI18nObj(i18nObj);
                } catch (Exception e) {
                    logger.error("解析i18n字段失败: {}", e.getMessage());
                }
            }
            
            // 处理products字段 - 直接使用Fastjson解析
            if (sitePayInfo.getProducts() != null) {
                try {
                    List<ProductVO> productList = JSON.parseArray(sitePayInfo.getProducts(), ProductVO.class);
                    sitePayInfo.setProductList(productList);
                } catch (Exception e) {
                    logger.error("解析products字段失败: {}", e.getMessage());
                }
            }
        }
        
        return sitePayInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer edit(SitePayAddReqVO pageReqVO) {
        SitePay sitePay = BeanUtils.toBean(pageReqVO, SitePay.class);
        sitePayMapper.updateById(sitePay);

        sitePayI18nMapper.delete(SitePayI18n::getSiteId, sitePay.getId());
        List<SitePayI18n> bean = BeanUtils.toBean(pageReqVO.getI18ns(), SitePayI18n.class);
        bean.forEach( i18n -> {i18n.setSiteId(  sitePay.getId());});
        sitePayI18nMapper.insertBatch(bean);

        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addSku(List<SitePayAddSkuReqVO> reqVO) {
        if (CollectionUtils.isEmpty(reqVO)) {
            return 0;
        }

        int count = 0;

        for (SitePayAddSkuReqVO req : reqVO) {
            // 1. 转换主体数据
            SitePaySku skuEntity = BeanUtils.toBean(req, SitePaySku.class);

            // 2. 插入主表记录并获取ID
            sitePaySkuMapper.insert(skuEntity);
            Long skuId = skuEntity.getId(); // MyBatis自动填充生成的ID

            // 3. 处理多语言数据 - 如果存在
            List<SitePaySkuI18n> i18nList = new ArrayList<>();
            if (req.getI18ns() != null && !req.getI18ns().isEmpty()) {
                // 过滤掉空数据，只处理有效的多语言信息
                i18nList = req.getI18ns().stream()
                        .filter(i18n -> StringUtils.isNotBlank(i18n.getTitle()) || StringUtils.isNotBlank(i18n.getDescription()))
                        .map(i18n -> {
                            SitePaySkuI18n entity = BeanUtils.toBean(i18n, SitePaySkuI18n.class);
                            entity.setSkuId(skuId);
                            return entity;
                        })
                        .collect(Collectors.toList());
            }

            // 只有在有多语言数据时才进行插入
            if (!i18nList.isEmpty()) {
                sitePaySkuI18nMapper.insertOrUpdate(i18nList);
            }

            count++;
        }

        return count;


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteSku(Long id) {
        sitePaySkuMapper.deleteById(id);
        sitePaySkuI18nMapper.delete(SitePaySkuI18n::getSkuId, id);
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateSku(SitePayAddSkuReqVO reqVO) {
        SitePaySku bean = BeanUtils.toBean(reqVO, SitePaySku.class);
        sitePaySkuMapper.updateById(bean);

        sitePaySkuI18nMapper.delete(SitePaySkuI18n::getSkuId, reqVO.getId());
        List<SitePaySkuI18n> sitePaySkuI18ns = BeanUtils.toBean(reqVO.getI18ns(), SitePaySkuI18n.class);
        sitePaySkuI18nMapper.insertBatch(sitePaySkuI18ns);

        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteSku(List<Long> id) {

        sitePaySkuMapper.deleteByIds(id);
        sitePaySkuI18nMapper.delete(new MPJLambdaWrapperX<SitePaySkuI18n>().inIfPresent(SitePaySkuI18n::getSkuId, id));

        return 0;
    }
}
