package com.mandong.api.module.erp.service.pay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.app.pay.vo.CallBackPayerMaxRespVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmRespVO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.pay.*;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.role.SdkRoleDO;
import com.mandong.api.module.erp.dal.dataobject.site.SitePay;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.mysql.pay.SdkPaysMapper;
import com.mandong.api.module.erp.dal.mysql.site.SitePayMapper;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.role.SdkRoleMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.pay.dto.payermax.*;
import com.mandong.api.module.erp.service.pay.util.PayerMaxSignatureUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;
import static com.mandong.api.module.system.enums.ErrorCodeConstants.ROLE_NOT_EXISTS;

/**
 * 支付服务实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    @Resource
    private PayConfigService payConfigService;

    @Resource
    private PayMethodService payMethodService;

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private SdkOrderMapper sdkOrderMapper;

    @Resource
    private SdkPaysMapper sdkPaysMapper;


    @Resource
    private SitePayMapper sitePayMapper;

    @Resource
    private SdkRoleMapper  sdkRoleMapper;

    @Value("${mandong.payermax_api_url}")
    private String PAYERMAX_API_URL;

    @Override
    public PaymentConfirmRespVO confirmPayment(PaymentConfirmReqVO reqVO) {
        // 1. 验证支付配置
        PayConfigDO payConfig = validatePayConfig(reqVO.getConfigId());

        // 2. 验证支付方式
        PayMethodDO payMethod = validatePayMethod(reqVO.getMethodId());

        // 3. 验证支付配置和支付方式的关联关系
        validatePayConfigMethod(payConfig, reqVO.getMethodId());

        // 4. 执行业务验证（您可以在这里添加自定义验证逻辑）
        validateBusinessRules(reqVO);

        // 5. 根据支付提供商调用相应的支付接口
        return processPayment(payConfig, payMethod, reqVO);
    }



    /**
     * 验证支付配置
     */
    private PayConfigDO validatePayConfig(Long configId) {
        PayConfigDO payConfig = payConfigService.getPayConfig(configId);
        if (payConfig == null) {
            throw exception(PAY_CONFIG_NOT_EXISTS);
        }
        if (Boolean.TRUE.equals(payConfig.getStatus())) {
            throw exception(PAY_CONFIG_DISABLED);
        }
        return payConfig;
    }

    /**
     * 验证支付方式
     */
    private PayMethodDO validatePayMethod(Long methodId) {
        PayMethodDO payMethod = payMethodService.getPayMethod(methodId);
        if (payMethod == null) {
            throw exception(PAY_METHOD_NOT_EXISTS);
        }
        if (Boolean.FALSE.equals(payMethod.getStatus())) {
            throw exception(PAY_METHOD_DISABLED);
        }
        return payMethod;
    }

    /**
     * 验证支付配置和支付方式的关联关系
     */
    private void validatePayConfigMethod(PayConfigDO payConfig, Long methodId) {
        PayConfigDO configWithDetails = payConfigService.getPayConfigWithDetails(payConfig.getId());
        List<PayConfigMethodDO> methods = configWithDetails.getMethods();

        boolean found = methods.stream()
                .anyMatch(method -> method.getMethodId().equals(methodId) && Boolean.TRUE.equals(method.getStatus()));

        if (!found) {
            throw exception(PAY_CONFIG_METHOD_NOT_ENABLED);
        }
    }

    /**
     * 执行业务验证（您可以在这里添加自定义验证逻辑）
     */
    private void validateBusinessRules(PaymentConfirmReqVO reqVO) {
        // TODO: 在这里添加您的业务验证逻辑
        // 例如：
        // - 验证订单是否存在
        // - 验证订单状态是否允许支付
        // - 验证支付金额是否正确
        // - 验证用户权限等

        log.info("执行业务验证，用户ID：{}, 金额：{}", reqVO.getUserId(), reqVO.getAmount());
    }

    /**
     * 根据支付提供商处理支付
     */
    private PaymentConfirmRespVO processPayment(PayConfigDO payConfig, PayMethodDO payMethod, PaymentConfirmReqVO reqVO) {
        String provider = payConfig.getProvider();

        switch (provider.toUpperCase()) {
            case "PAYERMAX":
                return processPayerMaxPayment(payConfig, payMethod, reqVO);
            case "GOOGLE_PAY":
                return processGooglePayPayment(payConfig, payMethod, reqVO);
            // TODO: 添加其他支付提供商的处理
            default:
                throw exception(PAYMENT_PROVIDER_NOT_SUPPORTED);
        }
    }

    /**
     * 处理PayerMax支付
     */
    private PaymentConfirmRespVO processPayerMaxPayment(PayConfigDO payConfig, PayMethodDO payMethod, PaymentConfirmReqVO reqVO) {
        log.info("处理PayerMax支付，配置ID：{}, 方式：{}", payConfig.getId(), payMethod.getCode());

        try {
            SdkUserDO sdkUserDO = sdkUserMapper.selectOne(SdkUserDO::getUid, reqVO.getUserId());
            if (sdkUserDO == null) {
                throw exception(USER_NOT_EXISTS);
            }
            Long productId = sdkUserDO.getProductId();
            SdkProductDO sdkProductDO = sdkProductMapper.selectById(productId);
            if (sdkProductDO == null) {
                throw exception(PRODUCT_NOT_EXISTS);
            }

            // 1. 获取PayerMax的配置参数
            Map<String, String> configParams = getPayerMaxConfigParams(payConfig);

            // 2. 构建PayerMax请求参数
            PayerMaxRequestDTO payerMaxRequest = buildPayerMaxRequest(configParams, payMethod, reqVO,sdkProductDO);

            // 3. 调用PayerMax API
            PayerMaxResponseDTO payerMaxResponse = callPayerMaxAPI(payerMaxRequest, configParams);

            // 4. 构建响应
            return buildPaymentResponse(payerMaxResponse, reqVO,payMethod,sdkProductDO,sdkUserDO);

        } catch (Exception e) {
            log.error("PayerMax支付处理失败", e);
            throw exception(PAYMENT_ORDER_INVALID);
        }
    }

    /**
     * 处理Google Pay支付
     */
    private PaymentConfirmRespVO processGooglePayPayment(PayConfigDO payConfig, PayMethodDO payMethod, PaymentConfirmReqVO reqVO) {
        // TODO: 调用Google Pay的API
        log.info("处理Google Pay支付，配置ID：{}, 方式：{}", payConfig.getId(), payMethod.getCode());

        // 模拟Google Pay API调用
        PaymentConfirmRespVO response = new PaymentConfirmRespVO();
        response.setPaymentUrl("https://pay.google.com/pay?token=" + generatePaymentToken());
        response.setPaymentMethod("GOOGLE_PAY");
        response.setPaymentOrderNo(generatePaymentOrderNo());
        response.setThirdPartyOrderNo("GOOGLE_" + System.currentTimeMillis());
        response.setStatus("PENDING");
        response.setExpireTime(LocalDateTime.now().plusMinutes(15).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        return response;
    }

    /**
     * 生成支付令牌
     */
    private String generatePaymentToken() {
        return "TOKEN_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }

    /**
     * 生成支付订单号
     */
    private String generatePaymentOrderNo() {
        return "PAY_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_" + (int)(Math.random() * 10000);
    }

    /**
     * 获取PayerMax配置参数
     */
    private Map<String, String> getPayerMaxConfigParams(PayConfigDO payConfig) {
        List<PayConfigParamDO> params = payConfig.getParams();
        if (params == null || params.isEmpty()) {
            throw exception(PAY_CONFIG_NOT_EXISTS);
        }

        return params.stream()
                .collect(Collectors.toMap(
                    PayConfigParamDO::getParamKey,
                    PayConfigParamDO::getParamValue,
                    (existing, replacement) -> existing
                ));
    }

    /**
     * 构建PayerMax请求参数
     */
    private PayerMaxRequestDTO buildPayerMaxRequest(Map<String, String> configParams, PayMethodDO payMethod, PaymentConfirmReqVO reqVO,SdkProductDO sdkProductDO) {


        String callbackUrl = sdkProductDO.getCallbackUrl();


        PayerMaxRequestDTO request = new PayerMaxRequestDTO();
        request.setRequestTime(new DateTime().toString("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
        request.setAppId(configParams.get("merchantAppId"));
        request.setMerchantNo(configParams.get("mch_id"));

        // 构建业务数据
        PayerMaxDataDTO data = new PayerMaxDataDTO();
        String orderNo = "W00" + new DateTime().toString("yyyyMMddHHmmssSSS");
        data.setOutTradeNo(orderNo);
        data.setSubject(reqVO.getProductName());
        data.setTotalAmount(reqVO.getAmount().intValue()); // 转换为分
        data.setCurrency(StrUtil.isNotBlank(reqVO.getCurrency()) ? reqVO.getCurrency() : configParams.getOrDefault("currency", "KRW"));
        data.setCountry(configParams.getOrDefault("country", "KR"));
        data.setUserId(reqVO.getUserId());
        data.setFrontCallbackUrl(reqVO.getReturnUrl());
        data.setNotifyUrl("https://admin.manmanyouhudong.com/app-api/pay/callback/payerMax");

        // 构建支付详情
        PayerMaxPaymentDetailDTO paymentDetail = new PayerMaxPaymentDetailDTO();
        paymentDetail.setPaymentMethodType(payMethod.getType());
        paymentDetail.setTargetOrg(payMethod.getCode());
        data.setPaymentDetail(paymentDetail);


        // 构建商品详情
        List<PayerMaxGoodsDetailDTO> goodsDetails = new ArrayList<>();
        PayerMaxGoodsDetailDTO goodsDetail = new PayerMaxGoodsDetailDTO();
        goodsDetail.setGoodsId(reqVO.getSkuCode());
        goodsDetail.setGoodsName(reqVO.getProductName());
        goodsDetail.setQuantity("1");
        goodsDetail.setPrice(reqVO.getAmount().toString());
        goodsDetail.setGoodsCurrency(StrUtil.isNotBlank(reqVO.getCurrency()) ? reqVO.getCurrency() : configParams.getOrDefault("currency", "KRW"));
        goodsDetail.setGoodsCategory(reqVO.getProductName());
        goodsDetails.add(goodsDetail);
        data.setGoodsDetails(goodsDetails);

        request.setData(data);
        return request;
    }


    /**
     * 调用PayerMax API
     */
    private PayerMaxResponseDTO callPayerMaxAPI(PayerMaxRequestDTO request, Map<String, String> configParams) {
        try {
            // 1. 生成请求JSON字符串（保持格式一致性）
            String requestJson = JSONUtil.toJsonStr(request);
            log.info("调用PayerMax API，请求体：{}", requestJson);

            // 2. 生成签名
            String privateKey = configParams.get("privateKey");
            String sign = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);
            log.info("生成的签名：{}", sign);

            // 3. 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            headers.put("sign", sign);

            // 5. 发送HTTP请求到PayerMax（签名作为URL参数）
            log.info("请求URL ：{}", PAYERMAX_API_URL  );

            String responseJson = HttpUtil.createPost(PAYERMAX_API_URL)
                    .body(requestJson)  // 请求体为JSON
                    .headerMap(headers, true)
                    .execute()
                    .body();

            log.info("PayerMax响应：{}", responseJson);

            // 6. 解析响应
            PayerMaxResponseDTO response = JSONUtil.toBean(responseJson, PayerMaxResponseDTO.class);

            // 7. 验证响应签名（可选，暂时跳过）
            // TODO: 根据PayerMax文档实现响应签名验证
            log.info("PayerMax响应处理完成");

            return response;

        } catch (Exception e) {
            log.error("调用PayerMax API失败", e);
            throw exception(PAYMENT_ORDER_INVALID);
        }
    }

    /**
     * 构建支付响应
     */
    private PaymentConfirmRespVO buildPaymentResponse(PayerMaxResponseDTO payerMaxResponse, PaymentConfirmReqVO reqVO,PayMethodDO payMethod,SdkProductDO sdkProductDO,SdkUserDO sdkUserDO) {
        PaymentConfirmRespVO response = new PaymentConfirmRespVO();

        if ("APPLY_SUCCESS".equals(payerMaxResponse.getCode())) {
            SitePay sitePay = sitePayMapper.selectById(reqVO.getSiteId());
            PayerMaxResponseDataDTO data = payerMaxResponse.getData();
            response.setPaymentUrl(data.getRedirectUrl());
            response.setPaymentOrderNo(data.getOutTradeNo());
            response.setThirdPartyOrderNo(data.getTradeToken());
            response.setStatus(data.getStatus());
            // 区服ID|@|角色ID|@|商品ID
            String extrasParams = reqVO.getServerId() + "|@|" + reqVO.getRoleId() + "|@|" + reqVO.getSkuCode();
            if (sdkProductDO.getGamePid() != null) {
                extrasParams += "|@|" + sdkProductDO.getGamePid();
            }
            SdkPays sdkPays = sdkPaysMapper.selectList(new LambdaQueryWrapperX<SdkPays>().like(SdkPays::getPayname, payMethod.getCode().toLowerCase())).stream().findFirst().orElse(null);
            SdkRoleDO sdkRoleDO = sdkRoleMapper.selectOne(SdkRoleDO::getGameRoleId, reqVO.getRoleId(),SdkRoleDO::getUid,reqVO.getUserId(),SdkRoleDO::getProductId,sdkProductDO.getId());
            if (sdkRoleDO == null) {
                throw exception(ROLE_NOT_EXISTS);
            }
            Integer payType;
            if (sdkPays != null) {
                payType = sdkPays.getId();
            } else {
                payType = 309;
            }
            String finalExtrasParams = extrasParams;
            SdkOrderDO sdkOrderDO = BeanUtils.toBean(reqVO, SdkOrderDO.class, in -> in
                    .setManageUid(2L)
                    .setOrderNo(data.getOutTradeNo())
                    .setProductId(sdkProductDO.getId())
                    .setChannelCode(sdkUserDO.getChannelCode())
                    .setUid(sdkUserDO.getUid())
                    .setUsername(sdkUserDO.getUsername())
                    .setCurrencyCode("₩")
                    .setCurrencyWord(reqVO.getCurrency())
                    .setAmount(Float.parseFloat(reqVO.getAmount().toString()))
                    .setCreateTime(new cn.hutool.core.date.DateTime().toTimestamp().getTime()/1000)
                    .setChannelOrderNo(data.getTradeToken())
                    .setPayStatus("0")
                    .setUsdAmount(Float.parseFloat(reqVO.getAmount().toString()))
                    .setPayType(payType)
                    .setOrderGoodsId(reqVO.getSkuCode())
                    .setOrderSubject(reqVO.getProductName())
                    .setCallbackUrl(sitePay.getCallBackUrl())
                    .setServerName(sdkRoleDO.getServerName())
                    // 区服ID|@|角色ID|@|商品ID
                    .setExtrasParams(finalExtrasParams)



            );
            sdkOrderMapper.insert(sdkOrderDO);


        } else {
            throw exception(PAYMENT_ORDER_INVALID);
        }

        return response;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public JSONObject payerMaxCallback(CallBackPayerMaxRespVO body) {
        log.info("PayerMax回调：{}", JSONUtil.toJsonStr(body));
        JSONObject json = new JSONObject();

        if (body.getCode().equals("APPLY_SUCCESS")&&body.getData().getStatus().equals("SUCCESS")){
            SdkOrderDO sdkOrderDO = sdkOrderMapper.selectOne(SdkOrderDO::getOrderNo, body.getData().getOutTradeNo());
            if (sdkOrderDO == null) {
                throw exception(ORDER_NOT_EXISTS);
            }
            sdkOrderDO.setPayStatus("1");

            sdkOrderDO.setPayTime(DateUtil.parse(body.getData().getCompleteTime()).getTime()/1000);
            sdkOrderDO.setChannelOrderNo(body.getData().getTradeToken());
            sdkOrderDO.setDealAmount(body.getData().getTotalAmount());
            sdkOrderDO.setDealUsdAmount(body.getData().getTotalAmount());
            sdkOrderDO.setPayMessage(body.getData().getStatus());
            sdkOrderMapper.updateById(sdkOrderDO);
            json.put("code", "SUCCESS");
            json.put("msg", "Success");
            return json;

        }
        log.error("PayerMax回调失败：{}", JSONUtil.toJsonStr(body));
        json.put("code", "FAILED");
        json.put("msg", "FAILED");
        return json;
    }
}