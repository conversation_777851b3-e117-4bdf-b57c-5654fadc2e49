package com.mandong.api.module.erp.controller.admin.leadGroup.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class LeadGroupLinkAddReqVO {

    @NotBlank
    private String serverName;
    @NotNull
    private Long linkId;

    @NotEmpty
    private List<ProductVO> productList;

    @NotNull
    private String optGroupName;

    @Data
    public static class ProductVO{
        private Long productId;
        private String productName;
    }
}

