<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.site.SitePaySkuMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.site.SitePaySku">
    <!--@mbg.generated-->
    <!--@Table site_pay_sku-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="site_id" jdbcType="BIGINT" property="siteId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, site_id, sku_id, sku_name, price, currency, img_url, `status`, create_time, updater, 
    update_time, deleted, tenant_id
  </sql>
</mapper>