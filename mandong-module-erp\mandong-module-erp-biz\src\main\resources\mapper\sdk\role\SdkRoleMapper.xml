<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.role.SdkRoleMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <!--  <select id="page" resultType="com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO">
        select a.orderNo,
               p.productName,
               qc.channelName,
               a.username,
               a.roleName,
               a.roleId,
               a.serverName,
               a.roleLevel,
               a.orderGoodsId,
               a.dealAmount,
               qp.payName,
               a.payTime,
               a.payStatus
        from qsdk_order a
                 left join qsdk_product p on a.productId = p.id
                 left join quick_sdk_platform.qsdk_channel qc on a.productId = qc.productId and a.channelCode = qc.channelCode
                 left join qsdk_pays qp on a.payType = qp.id
        where a.payStatus = 1
          and a.asyncStatus = 1
        order by a.createTime desc
    </select>-->

    <select id="selectRolePageNew" resultType="com.mandong.api.module.erp.controller.admin.role.vo.RolePageRespVO">
        SELECT t.id,
               t.uid,
               t.channelCode,
               t1.productId,
               t.serverName,
               t.userName,
               t.gameRoleName,
               t.gameRoleId,
               t.vipLevel,
               t.gameRoleLevel,
               t.lastLoginTime,
               t.loginNum,
               t.payAmount,
               t.payNum,
               t.createTime,
               t.ip,
               t.deviceId,
               channel.channelName,
               t3.productName,
               IF(o.user_name is null,channel.channelName,o.user_name) as optName
        FROM qsdk_user t1
                 LEFT JOIN   qsdk_roles t ON (t1.uid = CONVERT(t.uid, UNSIGNED))
                 LEFT JOIN qsdk_channel channel ON (channel.productId = t.productId AND channel.channelCode = t.channelCode)
                 LEFT JOIN qsdk_product t3 ON (t3.id = t1.productId)
                 left join qsdk_opt_link ol on ol.product_id = t.productId and ol.channel_code = t.channelCode
                 left join qsdk_opt o on ol.link_id = o.id
        <where>
            <if test="reqVO.productId != null and reqVO.productId.size() != 0">
               and t1.productId in
                <foreach collection="reqVO.productId" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>

            </if>
            <if test="reqVO.channelCode != null and reqVO.channelCode.size() != 0">
                and t1.channelCode in
                <foreach collection="reqVO.channelCode" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="reqVO.lastLoginTime != null and reqVO.lastLoginTime.length != 0">
                and t.lastLoginTime between #{reqVO.lastLoginTime[0]} and #{reqVO.lastLoginTime[1]}
            </if>

            <if test="reqVO.roleId != null">
                and t.gameRoleId = #{reqVO.roleId}
            </if>

            <if test="reqVO.roleName != null and reqVO.roleName != ''">
                and t.gameRoleName = #{reqVO.roleName}
            </if>

            <if test="reqVO.uid != null">
                and t1.uid = #{reqVO.uid}
            </if>

            <if test="reqVO.serverName != null and reqVO.serverName != ''">
                and t.serverName = #{reqVO.serverName}
            </if>

            <if test="reqVO.username != null and reqVO.username != ''">
                and t1.username = #{reqVO.username}
            </if>

            <if test="conditions != null and conditions.size() != 0">
                and (
                <foreach collection="conditions" item="item" separator="or"  >
                    ( t1.productId =   #{item.productId} and t1.channelCode = #{item.channelCode} )
                </foreach>
                )
            </if>
        </where>
        ORDER BY t.lastLoginTime DESC
    </select>
</mapper>