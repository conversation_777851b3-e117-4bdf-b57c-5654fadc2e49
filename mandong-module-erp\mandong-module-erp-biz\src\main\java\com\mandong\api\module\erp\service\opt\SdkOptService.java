package com.mandong.api.module.erp.service.opt;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.opt.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;

import java.util.List;

public interface SdkOptService {
    PageResult<OptRespVO> getOptPage(OptPageReqVO pageReqVO);
    PageResult<OptLinksPageRespVO> getOptLinksPage(OptLinksPageReqVO pageReqVO);

    int delete(long id);
    int deleteGroupLinks(long id);
    int add(OptAddReqVO reqVO);
    int createGroupLinks(OptLinkAddReqVO reqVO);

    List<SdkOptLinkDO> getOptLinks(long id);

    List<OptMonthlyStatsVO> getOptMonthlyStats(Long id,Long  startTime,Long  endTime );
}
