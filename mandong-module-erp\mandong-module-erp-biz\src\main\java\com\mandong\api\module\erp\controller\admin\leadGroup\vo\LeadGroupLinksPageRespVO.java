package com.mandong.api.module.erp.controller.admin.leadGroup.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 带队归属 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LeadGroupLinksPageRespVO {
    private Long id;

    @Schema(description = "组名")
    private String groupName;

    @Schema(description = "带队账号")
    private String userName;


    @Schema(description = "游戏id")
    private Integer productId;

    @Schema(description = "游戏名")
    private String productName;

    @Schema(description = "区服")
    private String serverName;

    private String optId;
    private String optGroupName;

    private String optUserName;


    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
