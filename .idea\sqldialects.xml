<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/dal/sdkMysql/order/SdkOrderMapper.java" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/dal/sdkMysql/role/SdkRoleMapper.java" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/resources/mapper/sdk/leadGroup/SdkLeadGroupLinkMapper.xml" dialect="MySQL" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/resources/mapper/sdk/opt/SdkOptLinkMapper.xml" dialect="GenericSQL" />
  </component>
</project>