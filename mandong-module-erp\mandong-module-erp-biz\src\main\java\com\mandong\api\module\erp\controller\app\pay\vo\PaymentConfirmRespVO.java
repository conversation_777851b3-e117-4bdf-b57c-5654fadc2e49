package com.mandong.api.module.erp.controller.app.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "支付页面 - 支付确认 Response VO")
@Data
public class PaymentConfirmRespVO {

    @Schema(description = "支付链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://payermax.com/pay?token=xxx")
    private String paymentUrl;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    private String paymentMethod;

    @Schema(description = "支付订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAY_20231201_001")
    private String paymentOrderNo;

    @Schema(description = "第三方订单号", example = "PAYERMAX_20231201_001")
    private String thirdPartyOrderNo;

    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "PENDING")
    private String status;

    @Schema(description = "过期时间", example = "2023-12-01T12:00:00")
    private String expireTime;

    @Schema(description = "扩展信息", example = "{\"qrCode\":\"data:image/png;base64,xxx\"}")
    private String extraInfo;

}
