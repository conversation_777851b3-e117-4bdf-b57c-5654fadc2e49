package com.mandong.api.module.erp.controller.admin.site.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 支付网站登录配置表
 */
@Data
public class SitePayLoginVO {
    /**
     * id
     */
    private Long id;

    /**
     * 支付网站id
     */
    private Long sitePayId;

    /**
     * 登录方式
     */
    private Long type;

    /**
     * 登录方式名称
     */
    private String name;

    /**
     * 谷歌登录参数
     */
    private String clientId;
}