package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_short_link")
public class SdkShortLinkDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "linkCode")
    private String linkCode;

    @TableField(value = "manageUid")
    private Integer manageUid;

    @TableField(value = "productId")
    private Integer productId;

    @TableField(value = "productCode")
    private String productCode;

    @TableField(value = "channelCode")
    private String channelCode;

    /**
     * 下载链接
     */
    @TableField(value = "downloadLink")
    private String downloadLink;

    @TableField(value = "downloadiOS")
    private String downloadiOS;

    @TableField(value = "downloadAndroid")
    private String downloadAndroid;

    /**
     * 是否使用落地页
     */
    @TableField(value = "useAdPage")
    private Boolean useAdPage;

    @TableField(value = "adPageId")
    private Integer adPageId;

    /**
     * 系统创建
     */
    @TableField(value = "systemAuto")
    private Boolean systemAuto;

    @TableField(value = "useHostId")
    private Integer useHostId;

    @TableField(value = "clickNum")
    private Integer clickNum;
}