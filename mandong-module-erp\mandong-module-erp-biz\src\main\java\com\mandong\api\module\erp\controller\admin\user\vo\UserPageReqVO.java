package com.mandong.api.module.erp.controller.admin.user.vo;

import com.mandong.api.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserPageReqVO extends PageParam {
    @Schema(description = "用户名", example = "赵六")
    private String username;

    @Schema(description = "用户id", example = "12")
    private Long uid;
    private List<Integer> productId;
    private List<String> channelCode;
    @Schema(description = "游戏名")
    private List<String> gameName;

    @Schema(description = "注册时间")
    private Long[] regTime;
}
