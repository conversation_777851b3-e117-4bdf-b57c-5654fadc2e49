package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@TableName("qsdk_pack_task")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkPackTaskDO {
    @TableId
    private Integer id;
    @TableField("manageUid")
    private Integer manageUid;

    @TableField("productId")
    private Integer productId;
    @TableField("signId")
    private Integer signId;

    @TableField("ftpId")
    private Integer ftpId;

    @TableField("createTime")
    private Long createTime;

    @TableField("gameVersionId")
    private Integer gameVersionId;

    /**
    * 版本名
    */
    @TableField("gameVersionName")
    private String gameVersionName;

    @TableField("taskName")
    private String taskName;

    @TableField("savePackName")
    private String savePackName;

    @TableField("cpsNum")
    private Integer cpsNum;

    @TableField("cpsList")
    private String cpsList;

    /**
    * 0正在排队 1正在执行 2执行成功 3执行失败
    */
    @TableField("nowStatus")
    private Boolean nowStatus;

    @TableField("messageTips")
    private String messageTips;

    @TableField("runTime")
    private Integer runTime;

    @TableField("endTime")
    private Integer endTime;

    @TableField("appName")
    private String appName;

    /**
    * 落地页
    */
    @TableField("gameIcon")
    private String gameIcon;

    @TableField("initImage")
    private String initImage;

    @TableField("otherFiles")
    private String otherFiles;

    @TableField("useHostId")
    private Integer useHostId;

    /**
    * 运行程序ID
    */
    @TableField("runProcess")
    private Integer runProcess;

    /**
    * 1正常,2回收站
    */
    @TableField("status")
    private Integer status;
}