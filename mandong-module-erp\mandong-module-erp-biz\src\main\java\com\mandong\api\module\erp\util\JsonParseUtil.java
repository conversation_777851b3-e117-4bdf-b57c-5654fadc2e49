package com.mandong.api.module.erp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * JSON解析工具类
 */
public class JsonParseUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonParseUtil.class);

    /**
     * 将JSON字符串解析为JSONObject对象
     *
     * @param jsonStr JSON字符串
     * @return JSONObject对象，解析失败返回null
     */
    public static JSONObject parseObject(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonStr);
        } catch (Exception e) {
            logger.error("JSON解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将JSON字符串解析为JSONArray对象
     *
     * @param jsonStr JSON字符串
     * @return JSONArray对象，解析失败返回null
     */
    public static JSONArray parseArray(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseArray(jsonStr);
        } catch (Exception e) {
            logger.error("JSON数组解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将JSON字符串解析为指定类型的对象
     *
     * @param jsonStr JSON字符串
     * @param clazz   目标类型
     * @param <T>     泛型参数
     * @return 解析结果，解析失败返回null
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            logger.error("JSON对象解析为{}失败: {}", clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 将JSON字符串解析为指定类型的对象列表
     *
     * @param jsonStr JSON字符串
     * @param clazz   目标类型
     * @param <T>     泛型参数
     * @return 解析结果列表，解析失败返回空列表
     */
    public static <T> List<T> parseArray(String jsonStr, Class<T> clazz) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return JSON.parseArray(jsonStr, clazz);
        } catch (Exception e) {
            logger.error("JSON数组解析为{}列表失败: {}", clazz.getSimpleName(), e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 从JSONObject中安全获取指定键的值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 对应的值，不存在或异常时返回null
     */
    public static String getString(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        return jsonObject.getString(key);
    }

    /**
     * 从JSONObject中获取嵌套的JSONObject
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 嵌套的JSONObject，不存在或异常时返回null
     */
    public static JSONObject getJSONObject(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        try {
            return jsonObject.getJSONObject(key);
        } catch (Exception e) {
            logger.error("获取嵌套JSONObject失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从JSONObject中获取嵌套的JSONArray
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 嵌套的JSONArray，不存在或异常时返回null
     */
    public static JSONArray getJSONArray(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        try {
            return jsonObject.getJSONArray(key);
        } catch (Exception e) {
            logger.error("获取嵌套JSONArray失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 对象
     * @return JSON字符串，转换失败返回null
     */
    public static String toJSONString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return JSON.toJSONString(object);
        } catch (Exception e) {
            logger.error("对象转换为JSON字符串失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从多语言JSON对象中获取指定语言的值
     *
     * @param i18nJsonObject 多语言JSON对象
     * @param field 字段名（如title、description等）
     * @param locale 语言代码（如zh、en、ko等）
     * @return 对应语言的值，不存在返回null
     */
    public static String getI18nValue(JSONObject i18nJsonObject, String field, String locale) {
        if (i18nJsonObject == null || !i18nJsonObject.containsKey(field)) {
            return null;
        }
        
        JSONObject fieldObj = i18nJsonObject.getJSONObject(field);
        if (fieldObj == null || !fieldObj.containsKey(locale)) {
            return null;
        }
        
        return fieldObj.getString(locale);
    }
} 