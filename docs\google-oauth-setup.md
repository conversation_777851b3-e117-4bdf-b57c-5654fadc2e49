# Google OAuth 登录配置指南

## 1. Google OAuth 配置

### 1.1 在 Google Cloud Console 中创建项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API 或 Google Identity API

### 1.2 创建 OAuth 2.0 客户端 ID

1. 在 Google Cloud Console 中，导航到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth client ID"
3. 选择应用程序类型为 "Web application"
4. 设置授权重定向 URI：`http://localhost:3001/auth/google/callback`
5. 记录生成的客户端 ID 和客户端密钥

### 1.3 配置应用程序

在 `application.yaml` 或环境变量中配置：

```yaml
google:
  oauth:
    client-id: "your-google-client-id"
    client-secret: "your-google-client-secret"
    redirect-uri: "http://localhost:3001/auth/google/callback"
```

或使用环境变量：
```bash
export GOOGLE_CLIENT_ID="your-google-client-id"
export GOOGLE_CLIENT_SECRET="your-google-client-secret"
export GOOGLE_REDIRECT_URI="http://localhost:3001/auth/google/callback"
```

## 2. API 接口使用

### 2.1 Google 登录接口

**接口路径：** `POST /admin-api/erp/game/google-login`

**请求参数：**
```json
{
  "code": "string",      // 必填：Google OAuth授权码
  "gameId": "string",    // 必填：游戏ID
  "productId": "string"  // 可选：产品ID
}
```

**成功响应：**
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "uid": 12345,
    "username": "用户名",
    "productName": "产品名称",
    "productId": 123,
    "channelName": "渠道名称",
    "isGuest": 0,
    "userStatus": 1,
    "regTime": *************,
    "totalAmount": 0.0,
    "loginTotal": 1,
    "deviceId": "设备ID",
    "callbackUrl": "回调URL",
    "callbackKey": "回调密钥",
    "productCode": "产品代码"
  }
}
```

## 3. 前端集成示例

### 3.1 获取 Google 授权码

```javascript
// 1. 重定向到 Google 授权页面
const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
  `client_id=${GOOGLE_CLIENT_ID}&` +
  `redirect_uri=${encodeURIComponent(REDIRECT_URI)}&` +
  `response_type=code&` +
  `scope=openid email profile&` +
  `state=${generateRandomState()}`;

window.location.href = googleAuthUrl;

// 2. 在回调页面获取授权码
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');
```

### 3.2 调用登录接口

```javascript
// 使用获取到的授权码调用登录接口
const loginResponse = await fetch('/admin-api/erp/game/google-login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    code: code,
    gameId: 'your-game-id',
    productId: 'your-product-id' // 可选
  })
});

const result = await loginResponse.json();
if (result.code === 0) {
  // 登录成功，处理用户信息
  console.log('用户信息:', result.data);
} else {
  // 登录失败
  console.error('登录失败:', result.msg);
}
```

## 4. 注意事项

1. **用户预注册：** 当前实现要求用户必须在系统中预先存在，Google 登录只是验证身份，不会自动创建新用户。

2. **用户名映射：** 系统使用 Google 用户的 openid 作为用户名来查找对应的用户记录。

3. **安全性：** 
   - 确保 HTTPS 在生产环境中启用
   - 定期轮换 Google OAuth 客户端密钥
   - 验证 state 参数以防止 CSRF 攻击

4. **错误处理：** 如果 Google 登录失败，接口会返回通用的登录失败错误，具体错误信息会记录在服务器日志中。

## 5. 故障排除

### 5.1 常见错误

- **invalid_client：** 检查客户端 ID 和密钥是否正确
- **redirect_uri_mismatch：** 确保重定向 URI 与 Google Console 中配置的完全一致
- **access_denied：** 用户拒绝了授权请求

### 5.2 调试建议

1. 检查 Google Cloud Console 中的 OAuth 配置
2. 验证应用程序配置文件中的参数
3. 查看服务器日志获取详细错误信息
4. 使用浏览器开发者工具检查网络请求
