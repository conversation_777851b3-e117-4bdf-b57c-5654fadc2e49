<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.site.SitePayLoginMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.site.SitePayLogin">
    <!--@mbg.generated-->
    <!--@Table site_pay_login-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="site_pay_id" jdbcType="BIGINT" property="sitePayId" />
    <result column="type" jdbcType="BIGINT" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, site_pay_id, `type`, `name`, creator, create_time, updater, update_time, deleted, 
    tenant_id
  </sql>
</mapper>