# 支付接口401认证问题解决方案

## 问题描述

支付确认接口 `/app-api/pay/confirm-payment` 返回 `{"code":401,"data":null,"msg":"账号未登录"}`，但该接口应该是无需登录的公开接口。

## 解决方案

### 1. 控制器层面的修改

**文件**: `AppPayConfigController.java`

```java
@PostMapping("/confirm-payment")
@Operation(summary = "确认支付")
@PermitAll                    // ✅ 添加了免认证注解
@PreAuthorize("permitAll()")  // ✅ 添加了Spring Security免认证注解
public CommonResult<PaymentConfirmRespVO> confirmPayment(@Valid @RequestBody PaymentConfirmReqVO reqVO) {
    PaymentConfirmRespVO response = paymentService.confirmPayment(reqVO);
    return success(response);
}
```

### 2. 安全配置层面的修改

**文件**: `mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/framework/security/config/SecurityConfiguration.java`

创建了ERP模块专用的安全配置：

```java
@Configuration(proxyBeanMethods = false, value = "erpSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("erpAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {
            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // 支付确认接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/confirm-payment")).permitAll();
                
                // 支付配置查询接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/config/list")).permitAll();
                
                // PayerMax回调接口，无需登录
                registry.requestMatchers(buildAppApi("/pay/notify/**")).permitAll();
                
                // 其他支付相关的公开接口
                registry.requestMatchers(buildAppApi("/pay/public/**")).permitAll();
            }
        };
    }
}
```

### 3. 全局配置层面的修改

**文件**: `mandong-server/src/main/resources/application.yaml`

修正了配置文件中的拼写错误，并添加了支付接口：

```yaml
mandong:
  security:
    permit-all-urls:  # ✅ 修正了拼写错误（之前是permit-all_urls）
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /app-api/pay/confirm-payment # ✅ ERP支付确认接口，不需要登录
      - /app-api/pay/notify/** # ✅ 支付回调接口，不需要登录
```

## 修改说明

### 1. 多层防护

我们采用了多层防护策略，确保接口能够正常访问：

- **注解层面**: `@PermitAll` + `@PreAuthorize("permitAll()")`
- **模块配置层面**: ERP模块的`AuthorizeRequestsCustomizer`
- **全局配置层面**: `mandong.security.permit-all-urls`

### 2. 路径匹配

确保路径匹配正确：
- 控制器路径: `/pay/confirm-payment`
- 完整API路径: `/app-api/pay/confirm-payment`
- 配置中使用: `buildAppApi("/pay/confirm-payment")` 自动添加前缀

### 3. 配置修正

修正了配置文件中的关键错误：
- `permit-all_urls` → `permit-all-urls` (下划线改为连字符)

## 验证方法

### 1. 使用测试脚本

运行 `PaymentTestScript.java`:

```bash
java PaymentTestScript
```

### 2. 使用单元测试

运行 `AppPayConfigControllerTest.java`:

```bash
mvn test -Dtest=AppPayConfigControllerTest
```

### 3. 使用curl命令

```bash
curl -X POST http://localhost:48080/app-api/pay/confirm-payment \
  -H "Content-Type: application/json" \
  -d '{
    "configId": 1,
    "methodId": 1,
    "amount": 10000,
    "currency": "TWD",
    "orderNo": "TEST_ORDER_123",
    "subject": "测试商品",
    "userId": "test_user",
    "notifyUrl": "https://example.com/notify",
    "returnUrl": "https://example.com/return"
  }'
```

## 预期结果

修改后，接口应该：

1. **不再返回401错误** - 无需登录即可访问
2. **返回业务响应** - 可能是成功响应或业务错误（如配置不存在）
3. **正常处理请求** - 进入业务逻辑处理

## 可能的响应

### ✅ 成功情况
```json
{
  "code": 0,
  "data": {
    "paymentUrl": "https://pay.example.com/...",
    "paymentMethod": "PAYERMAX",
    "paymentOrderNo": "PAY_123456",
    "status": "PENDING"
  },
  "msg": "success"
}
```

### ⚠️ 业务错误（正常）
```json
{
  "code": 500,
  "data": null,
  "msg": "支付配置不存在"
}
```

### ❌ 仍然认证失败（需要进一步排查）
```json
{
  "code": 401,
  "data": null,
  "msg": "账号未登录"
}
```

## 故障排查

如果仍然返回401错误，请检查：

1. **应用重启** - 确保配置生效
2. **路径匹配** - 确认请求路径正确
3. **Spring Security版本** - 确认注解兼容性
4. **其他拦截器** - 检查是否有其他认证拦截器
5. **日志输出** - 查看Spring Security的调试日志

## 总结

通过以上三层修改，支付确认接口应该能够正常访问，无需登录认证。这种多层防护的方式确保了即使某一层配置失效，其他层仍能提供保护。
