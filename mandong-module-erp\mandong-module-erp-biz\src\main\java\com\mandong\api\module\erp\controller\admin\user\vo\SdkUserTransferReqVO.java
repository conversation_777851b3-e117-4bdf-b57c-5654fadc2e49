package com.mandong.api.module.erp.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SdkUserTransferReqVO {
    @Schema(description = "需要转移的渠道")
    @NotEmpty(message = "需要转移的渠道不能为空")
    private String channelCode;

    @Schema(description = "转移规则")
    @NotNull
    private Integer transferRule;

    @Schema(description = "转移时间")
    private Long transferTime;
    @Schema(description = "用户id")
    @NotNull
    private Long uid;
    @Schema(description = "需要转移的渠道的产品")
    @NotNull(message = "需要转移的渠道的产品不能为空")
    private Long productId;

}
