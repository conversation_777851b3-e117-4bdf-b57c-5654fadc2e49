package com.mandong.api.module.erp.controller.admin.site.vo;

import com.mandong.api.module.erp.dal.dataobject.site.SitePaySkuI18n;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付网站表
 */
@Data
public class SitePaySkuRespVO {

    private Long id;
    /**
     * 支付网站id
     */
    private Long siteId;
    /**
     * 商品类型(0：货币  1：礼包)
     */
    private int skuType;

    /**
     * 商品id
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 币别 默认KRW
     */
    private String currency;

    /**
     * 商品图片地址
     */
    private String imgUrl;

    /**
     * sku状态（0正常 1停用）
     */
    private Byte status;

    /**
     * 多语言配置
     */

    private List<SitePaySkuI18n> i18ns;

}