package com.mandong.api.module.erp.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;

@TableName("qsdk_logs_user_login")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkLogsUserLoginDO {


    private Long id;
    /**
     *
     */
    @TableField("uid")
    private Integer uid;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 产品id
     */
    @TableField("productId")
    private Integer productId;

    /**
     * 运营code
     */
    @TableField("channelCode")
    private String channelCode;

    /**
     * 0 正常 1账号不存在 2密码错误
     */
    @TableField("loginStatus")
    private Boolean loginStatus;

    /**
     * 创建时间
     */
    @TableField("createTime")
    private Integer createTime;

    /**
     * 设备id
     */
    @TableField("deviceId")
    private String deviceId;

    /**
     * 登陆方式 1用户名  2自动登录 3短信 4游客
     */
    @TableField("loginType")
    private Boolean loginType;

    /**
     * sdk版本
     */
    @TableField("sdkVersion")
    private String sdkVersion;

    /**
     * 游戏版本
     */
    @TableField("gameVersion")
    private String gameVersion;

    /**
     * 注册时间
     */
    @TableField("regTime")
    private Date regTime;

    /**
     * 登录ip
     */
    @TableField("loginIp")
    private String loginIp;


}