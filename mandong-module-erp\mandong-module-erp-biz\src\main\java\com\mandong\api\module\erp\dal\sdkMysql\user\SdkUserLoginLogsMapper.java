package com.mandong.api.module.erp.dal.sdkMysql.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkLogsUserLoginDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Calendar;
import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkUserLoginLogsMapper extends BaseMapperX<SdkLogsUserLoginDO> {

    default PageResult<UserLoginPageRespVO> selectUserLoginPage(UserPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, UserLoginPageRespVO.class, new MPJLambdaWrapperX<SdkLogsUserLoginDO>()
                .selectAll(SdkLogsUserLoginDO.class)
                .eqIfPresent(SdkLogsUserLoginDO::getUid, pageReqVO.getUid())
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkLogsUserLoginDO::getProductId)

                .orderByDesc(SdkLogsUserLoginDO::getCreateTime)
        );
    }
}