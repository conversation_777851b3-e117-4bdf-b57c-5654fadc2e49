<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserDeviceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <!--  <select id="page" resultType="com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO">
        select a.orderNo,
               p.productName,
               qc.channelName,
               a.username,
               a.roleName,
               a.roleId,
               a.serverName,
               a.roleLevel,
               a.orderGoodsId,
               a.dealAmount,
               qp.payName,
               a.payTime,
               a.payStatus
        from qsdk_order a
                 left join qsdk_product p on a.productId = p.id
                 left join quick_sdk_platform.qsdk_channel qc on a.productId = qc.productId and a.channelCode = qc.channelCode
                 left join qsdk_pays qp on a.payType = qp.id
        where a.payStatus = 1
          and a.asyncStatus = 1
        order by a.createTime desc
    </select>-->
</mapper>