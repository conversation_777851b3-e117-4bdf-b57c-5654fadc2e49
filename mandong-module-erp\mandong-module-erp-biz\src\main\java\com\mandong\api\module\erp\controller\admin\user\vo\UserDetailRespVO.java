package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.mandong.api.framework.excel.core.annotations.DictFormat;
import com.mandong.api.framework.excel.core.convert.DictConvert;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class UserDetailRespVO {
    @Schema(description = "用户id", example = "22173")
    @ExcelProperty("用户id")
    private Long uid;

    @Schema(description = "用户名", example = "22173")
    @ExcelProperty("用户名")
    private String username;

    @Schema(description = "游戏名", example = "22173")
    @ExcelProperty("游戏名")
    private String productName;

    private Long productId;

    @Schema(description = "运营人员", example = "22173")
    @ExcelProperty("运营人员")
    private String channelName;

    @Schema(description = "是否游客", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否游客", converter = DictConvert.class)
    @DictFormat("erp_order_source")
    private int isGuest;
    /**
     * 0正常 1禁用
     */
    @Schema(description = "用户状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "用户状态", converter = DictConvert.class)
    private int userStatus;


    @Schema(description = "注册时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("注册时间")
    private Long regTime;

    @Schema(description = "总付费额")
    private Float totalAmount =0F;

    @Schema(description = "登录次数")
    private Long loginTotal;

    @Schema(description = "设备id")
    private String deviceId;


    private String callbackUrl;
    private String callbackKey;

    private String productCode;

}
