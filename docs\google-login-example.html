<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google登录示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px 0;
            background-color: #4285f4;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #3367d6;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google登录示例</h1>
        
        <div class="form-group">
            <label for="clientId">Google Client ID:</label>
            <input type="text" id="clientId" placeholder="输入您的Google Client ID">
        </div>
        
        <div class="form-group">
            <label for="gameId">游戏ID:</label>
            <input type="text" id="gameId" value="game123" placeholder="输入游戏ID">
        </div>
        
        <div class="form-group">
            <label for="productId">产品ID (可选):</label>
            <input type="text" id="productId" value="product123" placeholder="输入产品ID">
        </div>
        
        <div class="form-group">
            <label for="apiUrl">API地址:</label>
            <input type="text" id="apiUrl" value="http://localhost:48080/admin-api" placeholder="输入API基础地址">
        </div>
        
        <button class="btn" onclick="startGoogleLogin()">开始Google登录</button>
        
        <div id="result"></div>
        
        <h2>使用说明</h2>
        <ol>
            <li>在Google Cloud Console中创建OAuth 2.0客户端ID</li>
            <li>将客户端ID填入上面的输入框</li>
            <li>确保重定向URI设置为: <code>http://localhost:3001/auth/google/callback</code></li>
            <li>点击"开始Google登录"按钮</li>
            <li>完成Google授权后，系统会自动调用登录接口</li>
        </ol>
        
        <h2>配置示例</h2>
        <pre>
# application.yaml
google:
  oauth:
    client-id: "your-google-client-id"
    client-secret: "your-google-client-secret"
    redirect-uri: "http://localhost:3001/auth/google/callback"
        </pre>
    </div>

    <script>
        // 生成随机state参数
        function generateState() {
            return Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(36).substring(2, 15);
        }
        
        // 开始Google登录流程
        function startGoogleLogin() {
            const clientId = document.getElementById('clientId').value;
            const gameId = document.getElementById('gameId').value;
            const productId = document.getElementById('productId').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            if (!clientId) {
                showResult('请输入Google Client ID', 'error');
                return;
            }
            
            if (!gameId) {
                showResult('请输入游戏ID', 'error');
                return;
            }
            
            // 保存参数到localStorage
            localStorage.setItem('googleLogin_gameId', gameId);
            localStorage.setItem('googleLogin_productId', productId);
            localStorage.setItem('googleLogin_apiUrl', apiUrl);
            
            const state = generateState();
            localStorage.setItem('googleLogin_state', state);
            
            const redirectUri = 'http://localhost:3001/auth/google/callback';
            const scope = 'openid email profile';
            
            const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${encodeURIComponent(clientId)}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `response_type=code&` +
                `scope=${encodeURIComponent(scope)}&` +
                `state=${encodeURIComponent(state)}`;
            
            showResult('正在跳转到Google授权页面...', 'success');
            
            // 跳转到Google授权页面
            window.location.href = googleAuthUrl;
        }
        
        // 处理Google回调
        function handleGoogleCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const error = urlParams.get('error');
            
            if (error) {
                showResult(`Google授权失败: ${error}`, 'error');
                return;
            }
            
            if (!code) {
                return; // 不是回调页面
            }
            
            const savedState = localStorage.getItem('googleLogin_state');
            if (state !== savedState) {
                showResult('State参数不匹配，可能存在安全风险', 'error');
                return;
            }
            
            const gameId = localStorage.getItem('googleLogin_gameId');
            const productId = localStorage.getItem('googleLogin_productId');
            const apiUrl = localStorage.getItem('googleLogin_apiUrl');
            
            // 调用登录接口
            callLoginAPI(code, gameId, productId, apiUrl);
        }
        
        // 调用登录API
        async function callLoginAPI(code, gameId, productId, apiUrl) {
            try {
                showResult('正在调用登录接口...', 'success');
                
                const requestBody = {
                    code: code,
                    gameId: gameId
                };
                
                if (productId) {
                    requestBody.productId = productId;
                }
                
                const response = await fetch(`${apiUrl}/erp/game/google-login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    showResult(`登录成功！用户信息：\n${JSON.stringify(result.data, null, 2)}`, 'success');
                } else {
                    showResult(`登录失败：${result.msg}`, 'error');
                }
                
                // 清理localStorage
                localStorage.removeItem('googleLogin_state');
                localStorage.removeItem('googleLogin_gameId');
                localStorage.removeItem('googleLogin_productId');
                localStorage.removeItem('googleLogin_apiUrl');
                
            } catch (error) {
                showResult(`请求失败：${error.message}`, 'error');
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<pre>${message}</pre>`;
        }
        
        // 页面加载时检查是否是Google回调
        window.onload = function() {
            handleGoogleCallback();
        };
    </script>
</body>
</html>
