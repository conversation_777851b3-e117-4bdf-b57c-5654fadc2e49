package com.mandong.api.module.erp.controller.admin.user;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.service.user.SdkUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - SDK用户")
@RestController
@RequestMapping("/erp/user")
@Validated
public class SdkUserController {

@Resource
private SdkUserService sdkUserService;


    @GetMapping("/page")
    @Operation(summary = "获得用户分页")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:query')")
    public CommonResult<PageResult<UserPageRespVO>> getOrderPage(@Valid UserPageReqVO pageReqVO) {

        return success(sdkUserService.getUserPage(pageReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户详情")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:query')")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<UserDetailRespVO> getUserDetail(@RequestParam("id") Long id) {

        return success(sdkUserService.getUserDetail(id));
    }

    @GetMapping("/userDevicePage")
    @Operation(summary = "获得用户激活分页")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:query')")
    public CommonResult<PageResult<UserDevicePageRespVO>> getUserDevicePage(@Valid UserPageReqVO pageReqVO) {

        return success(sdkUserService.getUserDevicePage(pageReqVO));
    }

    @GetMapping("/userLoginPage")
    @Operation(summary = "获得用户登录记录分页")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:query')")
    public CommonResult<PageResult<UserLoginPageRespVO>> getUserLoginPage(@Valid UserPageReqVO pageReqVO) {

        return success(sdkUserService.getUserLoginPage(pageReqVO));
    }

    @PostMapping("/transferChannel")
    @Operation(summary = "转移渠道")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:transfer')")
    public CommonResult  transferChannel(@Valid @RequestBody SdkUserTransferReqVO reqVO) {

        return sdkUserService.transferChannel(reqVO);
    }


    @PutMapping("/update-status")
    @Operation(summary = "更新用户状态")
    @PreAuthorize("@ss.hasPermission('erp:gameUser:updateStatus')")
    public CommonResult  updateStatus(@Valid @RequestBody SdkUserUpdateReqVO reqVO) {
        sdkUserService.updateStatus(reqVO.getId(),reqVO.getStatus());
        return success(true);
    }
}
