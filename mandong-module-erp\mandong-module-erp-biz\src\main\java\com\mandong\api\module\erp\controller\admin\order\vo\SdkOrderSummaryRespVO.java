package com.mandong.api.module.erp.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - Sdk订单交易统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkOrderSummaryRespVO {

    @Schema(description = "今日交易金额")
    private Float todayPrice;
    @Schema(description = "当周交易金额")
    private Float weekPrice;
    @Schema(description = "今日交易量")
    private Long todayPay;
    @Schema(description = "当周交易量")
    private Long weekPay;
    @Schema(description = "今日付费用户数")
    private Long todayPayUser;
    @Schema(description = "当周付费用户数")
    private Long weekPayUser;
    @Schema(description = "今日注册数")
    private Long todayRegistration;
    @Schema(description = "当周注册数")
    private Long weekRegistration;

    @Schema(description = "今日活跃数")
    private Long todayLive;
    @Schema(description = "当周活跃数")
    private Long weekLive;

    @Schema(description = "日活跃用户数")
    private Long todayActiveUsers;

    @Schema(description = "周活跃用户数")
    private Long weekActiveUsers;

    @Schema(description = "月交易金额")
    private List<SdkOrderSummaryMonthRespVO> monthlyPrice;
    @Schema(description = "月交易量")
    private List<SdkOrderSummaryMonthRespVO> monthlyPay;
    @Schema(description = "月付费用户数")
    private List<SdkOrderSummaryMonthRespVO> monthlyPayUser;
    @Schema(description = "月注册数")
    private List<SdkOrderSummaryMonthRespVO> monthlyRegistration;
    @Schema(description = "月注册数")
    private List<SdkOrderSummaryMonthRespVO> monthlyLive;

    @Schema(description = "月活跃用户趋势")
    private List<SdkOrderSummaryMonthRespVO> monthlyActiveUsers;
}
