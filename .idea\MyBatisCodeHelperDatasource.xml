<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MyBatisCodeHelperDatasource">
    <option name="projectProfile">
      <ProjectProfile>
        <option name="controllerTemplateString" value="&#10;#* @vtlvariable name=&quot;tableName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;servicePackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfacePackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfaceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;controllerPackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;tableRemark&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;myDate&quot; type=&quot;java.util.Date&quot; *#&#10;#* @vtlvariable name=&quot;simpleDateFormat&quot; type=&quot;java.text.SimpleDateFormat&quot; *#&#10;package $!{controllerPackage};&#10;import $!{entityPackageName}.$!{entityClassName};&#10;###set($realServiceName = $!{serviceClassName}+'Impl')&#10;import $!{servicePackageName}.$!{serviceClassName};&#10;import org.springframework.web.bind.annotation.*;&#10;&#10;#set($serviceFirstLower = $!{serviceClassName.substring(0,1).toLowerCase()}+$!{serviceClassName.substring(1,$!{serviceClassName.length()})})&#10;import org.springframework.beans.factory.annotation.Autowired;&#10;&#10;/**&#10;* $!{tableRemark}($!{tableName})表控制层&#10;*&#10;* <AUTHOR> class $!{entityClassName}Controller {&#10;/**&#10;* 服务对象&#10;*/&#10;    @Autowired&#10;    private $!{serviceClassName} $!{serviceFirstLower};&#10;&#10;    /**&#10;    * 通过主键查询单条数据&#10;    *&#10;    * @param id 主键&#10;    * @return 单条数据&#10;    */&#10;    @GetMapping(&quot;selectOne&quot;)&#10;    public $!{entityClassName} selectOne(Integer id) {&#10;    return $!{serviceFirstLower}.selectByPrimaryKey(id);&#10;    }&#10;&#10;}" />
        <option name="database" value="MySql" />
        <option name="deleteByPrimayKeyEnabled" value="false" />
        <option name="generatedClassPathList">
          <list>
            <option value="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\dal\sdkMysql\user" />
            <option value="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\dal\dataobject\user" />
            <option value="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\leadGroup" />
            <option value="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\dal\dataobject\leadGroup" />
            <option value="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-system\mandong-module-system-biz\src\main\java\com\mandong\api\module\system\dal\dataobject\user" />
          </list>
        </option>
        <option name="insertMethodEnabled" value="false" />
        <option name="insertSelectiveMethodEnabled" value="false" />
        <option name="javaMapperPackage" value="com.mandong.api.module.erp.dal.mysql.order" />
        <option name="javaMapperPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java" />
        <option name="javaModelPackage" value="com.mandong.api.module.erp.dal.dataobject.order" />
        <option name="javaModelPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java" />
        <option name="lastDatabaseCrudChooseModuleName" value="mandong-module-erp-biz" />
        <option name="lombokDataAnnotation" value="true" />
        <option name="mapperAnnotaion" value="true" />
        <option name="moduleNameToPackageAndPathMap">
          <map>
            <entry key="mandong-module-erp-biz">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.mandong.api.module.erp.dal.mysql.order" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java" />
                  <option name="javaModelPacakge" value="com.mandong.api.module.erp.dal.dataobject.order" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.mandong.api.module.erp.service.site" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/site" />
                  <option name="javaServicePackage" value="com.mandong.api.module.erp.service.site" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/site" />
                  <option name="xmlPackage" value="mapper.order" />
                  <option name="xmlPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
          </map>
        </option>
        <option name="mybatisPlusIdType" value="AUTO" />
        <option name="removeTablePreName" value="qsdk" />
        <option name="selectByPrimaryKeyEnabled" value="false" />
        <option name="tableGenerateConfigs">
          <map>
            <entry key="mandong:pay_config">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="PayConfig" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:pay_config_method">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="PayConfigMethod" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:pay_config_param">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="PayConfigParam" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:pay_method">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="PayMethod" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:site_pay">
              <value>
                <TableGenerateConfig>
                  <option name="columnOverrideList">
                    <list>
                      <MyColumnOverride>
                        <option name="columnName" value="product_id" />
                        <option name="javaProperty" value="productId" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="icon_url" />
                        <option name="javaProperty" value="iconUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="banner_url" />
                        <option name="javaProperty" value="bannerUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="player_count" />
                        <option name="javaProperty" value="playerCount" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="call_back_url" />
                        <option name="javaProperty" value="callBackUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="server_url" />
                        <option name="javaProperty" value="serverUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="login_url" />
                        <option name="javaProperty" value="loginUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="pay_url" />
                        <option name="javaProperty" value="payUrl" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="create_time" />
                        <option name="javaProperty" value="createTime" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="update_time" />
                        <option name="javaProperty" value="updateTime" />
                      </MyColumnOverride>
                      <MyColumnOverride>
                        <option name="columnName" value="tenant_id" />
                        <option name="javaProperty" value="tenantId" />
                      </MyColumnOverride>
                    </list>
                  </option>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SitePay" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:site_pay_i18n">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SitePayI18n" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:site_pay_login_config">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SitePayLoginConfig" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:site_pay_sku">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SitePaySku" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="mandong:site_pay_sku_i18n">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SitePaySkuI18n" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_ad_page">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkAdPage" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="true" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_order_callgame">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkOrderCallgame" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_pack_gameapk">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkPackGameApk" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="true" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_pack_task">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="PackTask" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_pack_taskresult">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkPackTaskResult" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="true" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_pays">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkPays" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_short_hosts">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkShortHosts" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="true" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="quick_sdk_platform:qsdk_short_link">
              <value>
                <TableGenerateConfig>
                  <option name="deleteByPrimayKeyEnabled" value="false" />
                  <option name="generatedKey" value="id" />
                  <option name="insertMethodEnabled" value="false" />
                  <option name="insertSelectiveMethodEnabled" value="false" />
                  <option name="javaModelName" value="SdkShortLink" />
                  <option name="moduleName" value="mandong-module-erp-biz" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="selectByPrimaryKeyEnabled" value="false" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
                  <option name="updateByPrimaykeyEnabled" value="false" />
                  <option name="useActualColumnName" value="true" />
                </TableGenerateConfig>
              </value>
            </entry>
          </map>
        </option>
        <option name="updateByPrimaryKeySelectiveEnabled" value="false" />
        <option name="updateByPrimaykeyEnabled" value="false" />
        <option name="userMybatisPlus" value="true" />
        <option name="xmlMapperPackage" value="mapper.order" />
        <option name="xmlMapperPath" value="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/resources" />
      </ProjectProfile>
    </option>
  </component>
</project>