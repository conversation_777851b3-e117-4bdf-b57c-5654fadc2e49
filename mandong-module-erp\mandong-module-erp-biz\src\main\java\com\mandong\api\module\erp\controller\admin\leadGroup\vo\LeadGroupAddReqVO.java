package com.mandong.api.module.erp.controller.admin.leadGroup.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 带队归属 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LeadGroupAddReqVO {

    @NotBlank
    @Schema(description = "组名")
    private String groupName;
    @NotBlank
    @Schema(description = "带队账号")
    private String userName;
    @NotNull
    private Long uid;



}
