package com.mandong.api.module.erp.dal.dataobject.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 支付网站表
 */
@Data
@TableName(value = "site_pay")
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SitePay {
    /**
     * 支付网站id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 网站名称
     */
    @TableField(value = "`name`")
    private String name;
    @TableField(value = "product_id")
    private String productId;
    @TableField(value = "product_code")
    private String productCode;


    /**
     * 网站地址
     */
    @TableField(value = "url")
    private String url;

    /**
     * icon图
     */
    @TableField(value = "icon_url")
    private String iconUrl;

    /**
     * banner图
     */
    @TableField(value = "banner_url")
    private String bannerUrl;

    /**
     * 评分 5分制
     */
    @TableField(value = "rating")
    private BigDecimal rating;

    /**
     * 玩家人数
     */
    @TableField(value = "player_count")
    private String playerCount;

    /**
     * 网站状态（0正常 1停用）
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 回调接口
     */
    @TableField(value = "call_back_url")
    private String callBackUrl;

    /**
     * 区服接口
     */
    @TableField(value = "server_url")
    private String serverUrl;

    /**
     * 登录接口
     */
    @TableField(value = "login_url")
    private String loginUrl;

    /**
     * 支付接口
     */
    @TableField(value = "pay_url")
    private String payUrl;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
}