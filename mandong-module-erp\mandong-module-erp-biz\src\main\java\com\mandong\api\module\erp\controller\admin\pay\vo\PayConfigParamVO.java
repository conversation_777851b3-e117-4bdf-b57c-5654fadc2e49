package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 支付配置参数 VO")
@Data
public class PayConfigParamVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "配置编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long configId;

    @Schema(description = "参数键", requiredMode = Schema.RequiredMode.REQUIRED, example = "app_id")
    private String paramKey;

    @Schema(description = "参数值", requiredMode = Schema.RequiredMode.REQUIRED, example = "2021001234567890")
    private String paramValue;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "STRING")
    private String paramType;

    @Schema(description = "是否加密", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean isEncrypted;

    @Schema(description = "参数描述", example = "应用ID")
    private String description;

}
