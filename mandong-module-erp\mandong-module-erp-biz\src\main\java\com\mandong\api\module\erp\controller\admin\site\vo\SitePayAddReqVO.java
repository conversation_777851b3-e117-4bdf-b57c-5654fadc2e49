package com.mandong.api.module.erp.controller.admin.site.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付网站表
 */
@Data
public class SitePayAddReqVO {

    private Long id;
    /**
     * 网站名称
     */
    private String name;

    private List<SitePayI18nVO> i18ns;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 网站地址
     */
    private String url;

    /**
     * icon图
     */
    private String iconUrl;

    /**
     * banner图
     */
    private String bannerUrl;

    /**
     * 评分 5分制
     */
    private BigDecimal rating;

    /**
     * 玩家人数
     */
    private String playerCount;

    /**
     * 网站状态（0正常 1停用）
     */
    private Byte status;

    /**
     * 回调接口
     */
    private String callBackUrl;

    /**
     * 区服接口
     */
    private String serverUrl;

    /**
     * 登录接口
     */
    private String loginUrl;

    /**
     * 支付接口
     */
    private String payUrl;

}