package com.mandong.api.module.erp.dal.dataobject.order;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mandong.api.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("erp_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Integer id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 部门
     */
    private String department;
    /**
     * 推广
     */
    private String promotion;
    /**
     * 游戏名
     */
    private String gameName;
    /**
     * 角色id
     */
    private String userId;
    /**
     * 区服
     */
    private String service;
    /**
     * 角色名
     */
    private String roleName;
    /**
     * 金额（美元）
     */
    private String amountUsd;
    /**
     * 金额
     */
    private String amount;
    /**
     * 商品
     */
    private String goods;
    /**
     * 支付方式
     */
    private String payment;
    /**
     * 交易时间
     */
    private LocalDateTime payTime;
    /**
     * 交易号
     */
    private String transactionNumber;
    /**
     * 订单号
     */
    private String orderNumber;
    /**
     * 带队归属
     */
    private String teamBelong;
    /**
     * 注册时间
     */
    private LocalDateTime registrationTime;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 游戏单号
     */
    private String gameOrderNumber;
    /**
     * 完成时间
     */
    private LocalDateTime completionTime;
    /**
     * 用户ip
     */
    private String userIp;
    /**
     * 订单来源（1--龙成后台  2---sdk后台)
     *
     * 枚举 {@link TODO erp_order_source 对应的类}
     */
    private Integer orderSource;

}