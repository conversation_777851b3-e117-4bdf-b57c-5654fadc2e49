package com.mandong.api.module.erp.service.pay;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodListReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodPageReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayMethodSaveReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import com.mandong.api.module.erp.dal.mysql.pay.PayMethodMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.PAY_METHOD_NOT_EXISTS;

/**
 * 支付方式 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PayMethodServiceImpl implements PayMethodService {

    @Resource
    private PayMethodMapper payMethodMapper;

    @Override
    public Long createPayMethod(PayMethodSaveReqVO createReqVO) {
        // 插入
        PayMethodDO payMethod = BeanUtils.toBean(createReqVO, PayMethodDO.class);
        payMethodMapper.insert(payMethod);
        // 返回
        return payMethod.getId();
    }

    @Override
    public void updatePayMethod(PayMethodSaveReqVO updateReqVO) {
        // 校验存在
        validatePayMethodExists(updateReqVO.getId());
        // 更新
        PayMethodDO updateObj = BeanUtils.toBean(updateReqVO, PayMethodDO.class);
        payMethodMapper.updateById(updateObj);
    }

    @Override
    public void deletePayMethod(Long id) {
        // 校验存在
        validatePayMethodExists(id);
        // 删除
        payMethodMapper.deleteById(id);
    }

    private void validatePayMethodExists(Long id) {
        if (payMethodMapper.selectById(id) == null) {
            throw exception(PAY_METHOD_NOT_EXISTS);
        }
    }

    @Override
    public PayMethodDO getPayMethod(Long id) {
        return payMethodMapper.selectById(id);
    }

    @Override
    public List<PayMethodDO> getPayMethodList(PayMethodListReqVO reqVO) {
        return payMethodMapper.selectList(reqVO);
    }

    @Override
    public PageResult<PayMethodDO> getPayMethodPage(PayMethodPageReqVO pageReqVO) {
        return payMethodMapper.selectPage(pageReqVO);
    }

}
