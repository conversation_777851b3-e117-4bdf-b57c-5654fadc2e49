package com.mandong.api.module.erp.dal.sdkMysql.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDevicePageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserPageReqVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkLogsUserLoginDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDevicesDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkUserDeviceMapper extends BaseMapperX<SdkUserDevicesDO> {

    default PageResult<UserDevicePageRespVO> selectUserDevicePage(UserPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, UserDevicePageRespVO.class, new MPJLambdaWrapperX<SdkUserDevicesDO>()
                .selectAll(SdkUserDevicesDO.class)
                .eqIfPresent(SdkUserDevicesDO::getUid, pageReqVO.getUid())
                .leftJoin(SdkUserDO.class,SdkUserDO::getUid,SdkUserDevicesDO::getUid)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDevicesDO::getProductId)
                .orderByDesc(SdkUserDevicesDO::getActiveTime)
        );
    }
}