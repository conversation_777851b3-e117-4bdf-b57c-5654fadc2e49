package com.mandong.api.module.erp.dal.dataobject.leadGroup;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;
@TableName("qsdk_lead_group_link")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkLeadGroupLinkDO  {
    /**
     *
     */
    private Integer id;
    /**
     * 关联关系表id
     */
    private Long linkId;
    /**
     * 产品id
     */
    private Long productId;
    private String productName;

    /**
     * 区服
     */
    private String serverName;
    /**
     * 运营队伍
     */
    private  String optGroupName;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

}