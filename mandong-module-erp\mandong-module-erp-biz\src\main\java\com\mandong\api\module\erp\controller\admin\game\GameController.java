package com.mandong.api.module.erp.controller.admin.game;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.service.game.GameService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - SDK游戏（产品）")
@RestController
@RequestMapping("/erp/game")
@Validated
public class GameController {


    @Resource
    private GameService gameService;

    @GetMapping("/page")
    @Operation(summary = "获得游戏分页")
    @PreAuthorize("@ss.hasPermission('erp:game:query')")
    public CommonResult<PageResult<GamePageRespVO>> getOrderPage(@Valid GamePageReqVO pageReqVO) {

        return success(gameService.getPage(pageReqVO));
    }

    @GetMapping("/gameVersion")
    @Operation(summary = "获得游戏版本")
    @PreAuthorize("@ss.hasPermission('erp:game:query')")
    public CommonResult<List<GameVersionRespVO>> gameVersion(@RequestParam("id") Integer id) {

        return success(gameService.getGameVersion(id));
    }

    @GetMapping("/getUrl")
    @Operation(summary = "获得游戏下载链接")
    @PreAuthorize("@ss.hasPermission('erp:game:url')")
    public CommonResult<GameGetUrlRespVO> getUrl(@RequestParam("id") Integer id) {

        return success(null);
    }

    @GetMapping("/taskList")
    @Operation(summary = "获得游戏分包任务")
    @PreAuthorize("@ss.hasPermission('erp:game:query')")
    public CommonResult<PageResult<GameVersionRespVO>> getTaskPage(@Valid GameTaskListReqVO reqVO) {
        return success(gameService.getTaskPage(reqVO));
    }




    @GetMapping("/getShortHost")
    @Operation(summary = "获得游戏官网域名")
    @PreAuthorize("@ss.hasPermission('erp:game:query')")
    public CommonResult<List<SdkShortHostsDO>> getShortHost() {
        return success(gameService.getShortHost());
    }

    @GetMapping("/getAdPage")
    @Operation(summary = "获得游戏落地页")
    @PreAuthorize("@ss.hasPermission('erp:game:query')")
    public CommonResult<List<SdkAdPageDO>> getAdPage(@RequestParam("id") Integer id) {
        return success(gameService.getAdPage(id));
    }

    @PostMapping("/login")
    @Operation(summary = "登录")
    @PermitAll
    public CommonResult<UserDetailRespVO> login(@Valid @RequestBody GameLoginReqVO reqVO) {


        return success(gameService.login(reqVO));
    }

    @PostMapping("/google-login")
    @Operation(summary = "Google登录")
    @PermitAll
    public CommonResult<UserDetailRespVO> googleLogin(@Valid @RequestBody GameGoogleLoginReqVO reqVO) {
        return success(gameService.googleLogin(reqVO));
    }


}
