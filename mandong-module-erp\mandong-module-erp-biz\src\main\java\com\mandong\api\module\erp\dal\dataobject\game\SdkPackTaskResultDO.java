package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@TableName("qsdk_pack_taskresult")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkPackTaskResultDO {
    @TableId
    private Integer id;

    @TableField("taskId")
    private Integer taskId;

    @TableField("gameVersionId")
    private Integer gameVersionId;

    @TableField("channelCode")
    private String channelCode;

    @TableField("channelName")
    private String channelName;

    @TableField("apkUrl")
    private String apkUrl;

    @TableField("shortLinkId")
    private Integer shortLinkId;
}