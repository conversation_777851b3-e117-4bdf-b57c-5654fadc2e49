package com.mandong.api.module.erp.dal.sdkMysql.game;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortLinkDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("sdkDB")
public interface SdkShortLinkMapper extends BaseMapperX<SdkShortLinkDO> {

    default List<SdkShortLinkDO> getShortLink(Integer id){
        return selectList(SdkShortLinkDO::getProductId,id);
    }
}