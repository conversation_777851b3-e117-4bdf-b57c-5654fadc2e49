package com.mandong.api.module.erp.controller.app.pay;

import cn.hutool.json.JSONUtil;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import jakarta.annotation.Resource;

/**
 * AppPayConfigController 测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("local")
class AppPayConfigControllerTest {

    @Resource
    private MockMvc mockMvc;

    @Test
    void testConfirmPaymentWithoutAuth() throws Exception {
        // 构建测试请求
        PaymentConfirmReqVO reqVO = new PaymentConfirmReqVO();
        reqVO.setConfigId(1L);
        reqVO.setMethodId(1L);
        reqVO.setAmount(10000L); // 100.00元
        reqVO.setCurrency("TWD");
        reqVO.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        reqVO.setSubject("测试商品");
        reqVO.setUserId("test_user_001");
        reqVO.setNotifyUrl("https://example.com/notify");
        reqVO.setReturnUrl("https://example.com/return");

        String requestJson = JSONUtil.toJsonStr(reqVO);

        // 发送请求（不带认证信息）
        mockMvc.perform(MockMvcRequestBuilders.post("/app-api/pay/confirm-payment")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestJson))
                .andExpect(MockMvcResultMatchers.status().isOk()) // 期望返回200，而不是401
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists()); // 期望有响应数据
    }

    @Test
    void testConfirmPaymentAccessibility() throws Exception {
        // 测试接口是否可以访问（不验证具体业务逻辑）
        PaymentConfirmReqVO reqVO = new PaymentConfirmReqVO();
        reqVO.setConfigId(1L);
        reqVO.setMethodId(1L);
        reqVO.setAmount(1000L);
        reqVO.setCurrency("TWD");
        reqVO.setOrderNo("ACCESS_TEST_" + System.currentTimeMillis());
        reqVO.setSubject("访问测试");
        reqVO.setUserId("test_user");
        reqVO.setNotifyUrl("https://test.com/notify");
        reqVO.setReturnUrl("https://test.com/return");

        String requestJson = JSONUtil.toJsonStr(reqVO);

        // 发送请求
        mockMvc.perform(MockMvcRequestBuilders.post("/app-api/pay/confirm-payment")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestJson))
                .andExpect(MockMvcResultMatchers.status().isNotEqualTo(401)) // 不应该返回401未认证
                .andExpect(MockMvcResultMatchers.status().isNotEqualTo(403)); // 不应该返回403无权限
    }
}
