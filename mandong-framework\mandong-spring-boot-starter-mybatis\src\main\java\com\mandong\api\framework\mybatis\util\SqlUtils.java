package com.mandong.api.framework.mybatis.util;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SQL工具类，提供SQL操作的工具方法
 */
public class SqlUtils {

    /**
     * 将集合转换为字符串，用于SQL中的IN子句
     *
     * @param collection 集合对象
     * @return 转换后的字符串，例如 "'value1','value2','value3'"
     */
    public static String convertListToString(Collection<?> collection) {
        if (collection == null || collection.isEmpty()) {
            return "''"; // 返回一个空字符串，避免SQL错误
        }
        
        return collection.stream()
                .map(item -> item == null ? "''" : "'" + item.toString().replace("'", "''") + "'")
                .collect(Collectors.joining(","));
    }
} 