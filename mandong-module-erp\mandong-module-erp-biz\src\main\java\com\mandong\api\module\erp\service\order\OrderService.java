package com.mandong.api.module.erp.service.order;

import java.util.*;

import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import jakarta.validation.*;
import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.dal.dataobject.order.OrderDO;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.pojo.PageParam;

/**
 * 订单 Service 接口
 *
 * <AUTHOR>
 */
public interface OrderService {

    /**
     * 创建订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createOrder(@Valid OrderSaveReqVO createReqVO);

    /**
     * 更新订单
     *
     * @param updateReqVO 更新信息
     */
    void updateOrder(@Valid OrderSaveReqVO updateReqVO);

    /**
     * 删除订单
     *
     * @param id 编号
     */
    void deleteOrder(Integer id);

    /**
     * 获得订单
     *
     * @param id 编号
     * @return 订单
     */
    OrderDO getOrder(Integer id);

    /**
     * 获得订单分页
     *
     * @param pageReqVO 分页查询
     * @return 订单分页
     */
    PageResult<SdkOrderRespVo> getOrderPage(OrderPageReqVO pageReqVO);

    /**
     * 订单总金额
     * @param pageReqVO
     * @return
     */
    SdkOrderTotalAmountRespVo getOrderPageTotalAmount(OrderPageReqVO pageReqVO);
    SdkOrderSummaryRespVO getOrderPaySummary(SdkOrderSummaryReqVO pageReqVO);
    List<String> getServerByProductId(List<Long> productId);

}