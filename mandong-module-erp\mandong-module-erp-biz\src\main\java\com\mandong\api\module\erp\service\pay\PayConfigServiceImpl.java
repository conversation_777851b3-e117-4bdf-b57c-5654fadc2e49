package com.mandong.api.module.erp.service.pay;

import com.mandong.api.framework.common.enums.CommonStatusEnum;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigPageReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigSaveReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigMethodDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigParamDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO;
import com.mandong.api.module.erp.dal.mysql.pay.PayConfigMapper;
import com.mandong.api.module.erp.dal.mysql.pay.PayConfigMethodMapper;
import com.mandong.api.module.erp.dal.mysql.pay.PayConfigParamMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.stream.Collectors;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.PAY_CONFIG_NOT_EXISTS;

/**
 * 支付配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PayConfigServiceImpl implements PayConfigService {

    @Resource
    private PayConfigMapper payConfigMapper;
    @Resource
    private PayConfigParamMapper payConfigParamMapper;
    @Resource
    private PayConfigMethodMapper payConfigMethodMapper;
    @Resource
    private PayMethodService payMethodService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPayConfig(PayConfigSaveReqVO createReqVO) {
        // 插入支付配置
        PayConfigDO payConfig = BeanUtils.toBean(createReqVO, PayConfigDO.class);
        payConfigMapper.insert(payConfig);

        // 插入配置参数
        if (createReqVO.getParams() != null) {
            List<PayConfigParamDO> params = BeanUtils.toBean(createReqVO.getParams(), PayConfigParamDO.class);
            params.forEach(param -> param.setConfigId(payConfig.getId()));
            payConfigParamMapper.insertBatch(params);
        }

        // 插入支付方式
        if (createReqVO.getMethods() != null) {
            List<PayConfigMethodDO> methods = BeanUtils.toBean(createReqVO.getMethods(), PayConfigMethodDO.class);
            methods.forEach(method -> method.setConfigId(payConfig.getId()));
            payConfigMethodMapper.insertBatch(methods);
        }

        return payConfig.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePayConfig(PayConfigSaveReqVO updateReqVO) {
        // 校验存在
        validatePayConfigExists(updateReqVO.getId());

        // 更新支付配置
        PayConfigDO updateObj = BeanUtils.toBean(updateReqVO, PayConfigDO.class);
        payConfigMapper.updateById(updateObj);

        // 删除旧的配置参数和支付方式
        payConfigParamMapper.deleteByConfigId(updateReqVO.getId());
        payConfigMethodMapper.deleteByConfigId(updateReqVO.getId());

        // 插入新的配置参数
        if (updateReqVO.getParams() != null) {
            List<PayConfigParamDO> params = BeanUtils.toBean(updateReqVO.getParams(), PayConfigParamDO.class);
            params.forEach(param -> param.setConfigId(updateReqVO.getId()));
            payConfigParamMapper.insertBatch(params);
        }

        // 插入新的支付方式
        if (updateReqVO.getMethods() != null) {
            List<PayConfigMethodDO> methods = BeanUtils.toBean(updateReqVO.getMethods(), PayConfigMethodDO.class);
            methods.forEach(method -> method.setConfigId(updateReqVO.getId()));
            payConfigMethodMapper.insertBatch(methods);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePayConfig(Long id) {
        // 校验存在
        validatePayConfigExists(id);

        // 删除支付配置
        payConfigMapper.deleteById(id);
        // 删除配置参数
        payConfigParamMapper.deleteByConfigId(id);
        // 删除支付方式
        payConfigMethodMapper.deleteByConfigId(id);
    }

    private void validatePayConfigExists(Long id) {
        if (payConfigMapper.selectById(id) == null) {
            throw exception(PAY_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public PayConfigDO getPayConfig(Long id) {
        List<PayConfigParamDO> params = payConfigParamMapper.selectListByConfigId(id);
        return payConfigMapper.selectById(id).setParams(params);
    }

    @Override
    public PayConfigDO getPayConfigWithDetails(Long id) {
        PayConfigDO config = payConfigMapper.selectById(id);
        if (config == null) {
            return null;
        }

        // 加载配置参数
        List<PayConfigParamDO> params = payConfigParamMapper.selectListByConfigId(id);
        config.setParams(params);

        // 加载支付方式
        List<PayConfigMethodDO> methods = payConfigMethodMapper.selectListByConfigId(id);
        // 填充支付方式的详细信息
        for (PayConfigMethodDO method : methods) {
            PayMethodDO payMethod = payMethodService.getPayMethod(method.getMethodId());
            if (payMethod != null) {
                method.setMethodCode(payMethod.getCode());
                method.setMethodName(payMethod.getName());
            }
        }
        config.setMethods(methods);

        return config;
    }

    @Override
    public PageResult<PayConfigDO> getPayConfigPage(PayConfigPageReqVO pageReqVO) {
        return payConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public void updatePayConfigStatus(Long id, Integer status) {
        // 校验存在
        validatePayConfigExists(id);

        // 更新状态
        PayConfigDO updateObj = new PayConfigDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        payConfigMapper.updateById(updateObj);
    }

    @Override
    public List<PayConfigDO> getEnabledPayConfigList() {
        return payConfigMapper.selectListByStatus(CommonStatusEnum.ENABLE.getStatus());
    }

    @Override
    public List<PayConfigDO> getEnabledPayConfigListForPayment() {
        // 获取启用的支付配置（status = false，即0表示启用）
        List<PayConfigDO> enabledConfigs = payConfigMapper.selectListByStatus(false);

        // 为每个启用的支付配置加载启用的支付方式
        for (PayConfigDO config : enabledConfigs) {
            // 获取该配置下所有的支付方式关联
            List<PayConfigMethodDO> allMethods = payConfigMethodMapper.selectListByConfigId(config.getId());

            // 过滤出启用的支付方式并填充详细信息
            List<PayConfigMethodDO> enabledMethods = allMethods.stream()
                    .filter(method -> Boolean.TRUE.equals(method.getStatus()))
                    .map(method -> {
                        PayMethodDO payMethod = payMethodService.getPayMethod(method.getMethodId());
                        if (payMethod != null && Boolean.TRUE.equals(payMethod.getStatus())) {
                            // 只有支付方式本身也是启用状态才返回
                            method.setMethodCode(payMethod.getCode());
                            method.setMethodName(payMethod.getName());
                            method.setIconUrl(payMethod.getIconUrl());
                            method.setSort(payMethod.getSort());
                            return method;
                        }
                        return null;
                    })
                    .filter(method -> method != null)
                    .collect(Collectors.toList());

            config.setMethods(enabledMethods);
        }

        // 过滤掉没有启用支付方式的配置
        return enabledConfigs.stream()
                .filter(config -> config.getMethods() != null && !config.getMethods().isEmpty())
                .collect(Collectors.toList());
    }

}
