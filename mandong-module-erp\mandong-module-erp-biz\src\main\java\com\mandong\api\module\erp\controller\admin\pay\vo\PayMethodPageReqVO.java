package com.mandong.api.module.erp.controller.admin.pay.vo;

import com.mandong.api.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 支付方式分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PayMethodPageReqVO extends PageParam {

    @Schema(description = "支付提供商", example = "PAYERMAX")
    private String provider;

    @Schema(description = "状态", example = "1")
    private Integer status;

}
