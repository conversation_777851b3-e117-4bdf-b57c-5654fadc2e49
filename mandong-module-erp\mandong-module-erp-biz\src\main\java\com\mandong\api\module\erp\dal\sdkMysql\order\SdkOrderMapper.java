package com.mandong.api.module.erp.dal.sdkMysql.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.interfaces.MPJBaseJoin;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerChannelDetailsRespVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageReqVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单 Mapper - 主接口
 * 继承基础查询和统计汇总接口，避免单个接口过大导致lambda表达式字节码超限
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkOrderMapper extends SdkOrderBaseMapper, SdkOrderSummaryMapper {



    /**
     * 根据产品ID和服务器名称列表分页查询订单
     * 此方法支持按照产品ID和服务器名称进行交叉查询
     * 例如：用户有权访问 (产品1, 服务器A), (产品1, 服务器B), (产品2, 服务器C)
     * 如果仅查询产品1，则返回服务器A和B的数据
     *
     * @param pageReqVO   分页请求参数
     * @param productIds  有权限的产品ID列表
     * @param serverNames 有权限的服务器名称列表
     * @return 订单分页结果
     */
    default PageResult<SdkOrderRespVo> selectPageWithProductAndServers(OrderPageReqVO pageReqVO, List<Long> productIds, List<String> serverNames) {
        // 创建一个SQL查询包装器
        MPJLambdaWrapperX<SdkOrderDO> query = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectAll(SdkOrderDO.class)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .selectAs(SdkProductDO::getProductName, SdkOrderRespVo::getProductName);
        // 构建基础查询条件
        query.eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .eqIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .inIfPresent(SdkOrderDO::getChannelCode, pageReqVO.getChannelCode())
        ;

        // 添加时间范围条件
        if (pageReqVO.getCreateTime() != null && pageReqVO.getCreateTime().length == 2) {
            query.between(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime()[0], pageReqVO.getCreateTime()[1]);
        }

        // 将产品ID列表和区服列表转换为产品-区服的组合条件
        if (CollectionUtils.isNotEmpty(productIds) && CollectionUtils.isNotEmpty(serverNames)) {
            List<SdkOrderCondition> combinedConditions = new ArrayList<>();

            // 创建每个产品ID和区服名的组合条件
            for (Long productId : productIds) {
                for (String serverName : serverNames) {
                    SdkOrderCondition condition = new SdkOrderCondition();
                    condition.setProductId(productId);
                    condition.setServerName(serverName);
                    combinedConditions.add(condition);
                }
            }

            // 添加产品ID和服务器名称的组合条件 - 使用OR连接每个组合
            query.and(wrapper -> {
                for (int i = 0; i < combinedConditions.size(); i++) {
                    SdkOrderCondition condition = combinedConditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getServerName, condition.getServerName()));
                    } else {
                        wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getServerName, condition.getServerName()));
                    }
                }
            });
        } else {
            // 如果没有同时有产品ID和区服列表，则使用原来的逻辑
            query.and(wrapper -> {
                // 产品ID和服务器名称条件
                wrapper.nested(w -> {
                    if (CollectionUtils.isNotEmpty(productIds)) {
                        w.in(SdkOrderDO::getProductId, productIds);
                    }
                    if (CollectionUtils.isNotEmpty(serverNames)) {
                        w.and(sw -> sw.in(SdkOrderDO::getServerName, serverNames));
                    }
                });
            });
        }

        // 为了确保查询获取到所有关联信息，添加其他表连接
        query.select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on ->
                        on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .orderByDesc(SdkOrderDO::getId);

        // 执行分页查询
        PageResult<SdkOrderRespVo> resultPage = selectJoinPage(pageReqVO, SdkOrderRespVo.class, query);

        // 转换为自定义分页结果
        PageResult<SdkOrderRespVo> result = new PageResult<>();
        result.setList(resultPage.getList());
        result.setTotal(resultPage.getTotal());
        return result;
    }

    /**
     * 根据产品ID和服务器名称列表查询订单总金额
     * 此方法支持按照产品ID和服务器名称进行交叉查询
     *
     * @param pageReqVO   分页请求参数
     * @param productIds  有权限的产品ID列表
     * @param serverNames 有权限的服务器名称列表
     * @return 订单总金额
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmountWithProductAndServers(OrderPageReqVO pageReqVO, List<Long> productIds, List<String> serverNames) {
        // 创建一个SQL查询包装器
        MPJLambdaWrapperX<SdkOrderDO> query = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderTotalAmountRespVo::getTotalAmount)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode);


        // 构建基础查询条件
        query.eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .eqIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .inIfPresent(SdkOrderDO::getChannelCode, pageReqVO.getChannelCode())
        ;

        // 添加时间范围条件
        if (pageReqVO.getCreateTime() != null && pageReqVO.getCreateTime().length == 2) {
            query.between(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime()[0], pageReqVO.getCreateTime()[1]);
        }

        // 将产品ID列表和区服列表转换为产品-区服的组合条件
        if (CollectionUtils.isNotEmpty(productIds) && CollectionUtils.isNotEmpty(serverNames) && CollectionUtils.isNotEmpty(pageReqVO.getChannelCode())) {
            List<SdkOrderCondition> combinedConditions = new ArrayList<>();

            // 创建每个产品ID和区服名的组合条件
            for (Long productId : productIds) {
                for (String serverName : serverNames) {
                    SdkOrderCondition condition = new SdkOrderCondition();
                    condition.setProductId(productId);
                    condition.setServerName(serverName);
                    combinedConditions.add(condition);
                }
            }

            // 添加产品ID和服务器名称的组合条件 - 使用OR连接每个组合
            query.and(wrapper -> {
                for (int i = 0; i < combinedConditions.size(); i++) {
                    SdkOrderCondition condition = combinedConditions.get(i);
                    if (i == 0) {
                        if (condition.getChannelCode() == null) {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getServerName, condition.getServerName()).eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }

                    } else {
                        if (condition.getChannelCode() == null) {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getServerName, condition.getServerName()).eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                }
            });
        } else {
            // 如果没有同时有产品ID和区服列表，则使用原来的逻辑
            query.and(wrapper -> {
                wrapper.nested(w -> {
                    if (CollectionUtils.isNotEmpty(productIds)) {
                        w.in(SdkOrderDO::getProductId, productIds);
                    }
                    if (CollectionUtils.isNotEmpty(serverNames)) {
                        w.and(sw -> sw.in(SdkOrderDO::getServerName, serverNames));
                    }
                });
            });
        }

        // 执行查询获取总金额
        SdkOrderTotalAmountRespVo result = selectJoinOne(SdkOrderTotalAmountRespVo.class, query);
        return result;
    }

















    /**
     * 使用运营权限条件进行分页查询
     *
     * @param pageReqVO  查询条件
     * @param conditions 产品ID和渠道代码的组合条件
     * @return 分页结果
     */
    default PageResult<SdkOrderRespVo> selectPageWithOperationConditions(OrderPageReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            PageResult<SdkOrderRespVo> emptyResult = new PageResult<>();
            emptyResult.setList(new ArrayList<>());
            emptyResult.setTotal(0L);
            return emptyResult;
        }

        // 创建一个新的查询条件
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>().select(SdkOrderDO::getOrderNo, SdkOrderDO::getId, SdkOrderDO::getUid, SdkOrderDO::getChannelCode, SdkOrderDO::getUsername, SdkOrderDO::getRoleName, SdkOrderDO::getRoleId, SdkOrderDO::getRoleLevel, SdkOrderDO::getServerName, SdkOrderDO::getProductId, SdkOrderDO::getPayStatus, SdkOrderDO::getPayTime, SdkOrderDO::getPayType, SdkOrderDO::getAsyncStatus, SdkOrderDO::getCreateTime)
                .select("IF(t.payStatus=1,t.dealAmount,t.amount) AS dealAmount");

        // 添加通用查询条件
        queryWrapper.eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getUid, pageReqVO.getUid())
                .inIfExists(SdkOrderDO::getServerName, pageReqVO.getServers())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName());

        // 处理productId和channelCode的组合条件 - 使用原生SQL确保精确匹配
        if (!conditions.isEmpty()) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    } else {
                        wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    }
                }
            });
        }

        // 添加连接查询
        queryWrapper.select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on ->
                        on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o -> o.eq(SdkOrderDO::getProductId, SdkOptLinkDO::getProductId).eq(SdkOrderDO::getChannelCode, SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class, "o", SdkOptDO::getId, SdkOptLinkDO::getLinkId)
                .orderByDesc(SdkOrderDO::getId);

        // 执行查询
        return selectJoinPage(pageReqVO, SdkOrderRespVo.class, queryWrapper);
    }

    /**
     * 根据产品ID和渠道代码的精确组合查询订单
     * 该方法确保产品ID和渠道代码是一一对应的，不会出现交叉查询
     *
     * @param pageReqVO    分页请求参数
     * @param productIds   有权限的产品ID列表
     * @param channelCodes 有权限的渠道代码列表
     * @param combinations 产品ID和渠道代码的精确组合
     * @return 订单分页结果
     */
    default PageResult<SdkOrderRespVo> selectPageWithProductAndChannels(OrderPageReqVO pageReqVO,
                                                                        List<Long> productIds,
                                                                        List<String> channelCodes,
                                                                        List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> combinations) {
        // 创建一个SQL查询包装器
        MPJLambdaWrapperX<SdkOrderDO> query = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .select(SdkOrderDO::getOrderNo, SdkOrderDO::getId, SdkOrderDO::getUid, SdkOrderDO::getChannelCode, SdkOrderDO::getUsername, SdkOrderDO::getRoleName, SdkOrderDO::getRoleId, SdkOrderDO::getRoleLevel, SdkOrderDO::getServerName, SdkOrderDO::getProductId, SdkOrderDO::getPayStatus, SdkOrderDO::getPayTime, SdkOrderDO::getPayType, SdkOrderDO::getAsyncStatus, SdkOrderDO::getCreateTime)
                .select("IF(t.payStatus=1,t.dealAmount,t.amount) AS dealAmount")

                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .selectAs(SdkProductDO::getProductName, SdkOrderRespVo::getProductName);
        // 构建基础查询条件
        query.eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .eqIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .inIfExists(SdkOrderDO::getServerName, pageReqVO.getServers())
        ;

        // 添加时间范围条件
        if (pageReqVO.getCreateTime() != null && pageReqVO.getCreateTime().length == 2) {
            query.between(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime()[0], pageReqVO.getCreateTime()[1]);
        }

        if (pageReqVO.getPayTime() != null && pageReqVO.getPayTime().length == 2) {
            query.between(SdkOrderDO::getPayTime, pageReqVO.getPayTime()[0], pageReqVO.getPayTime()[1]);
        }

        // 处理用户请求的筛选条件
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();

        // 过滤合法的组合
        List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> filteredCombinations = new ArrayList<>(combinations);

        // 处理用户的查询条件
        if (hasUserProductId) {
            filteredCombinations = filteredCombinations.stream()
                    .filter(condition -> pageReqVO.getProductId().contains(condition.getProductId()))
                    .collect(Collectors.toList());
        }

        if (hasUserChannelCode) {
            filteredCombinations = filteredCombinations.stream()
                    .filter(condition -> pageReqVO.getChannelCode().contains(condition.getChannelCode()))
                    .collect(Collectors.toList());
        }

        // 如果没有有效的组合，返回空结果
        if (filteredCombinations.isEmpty()) {
            PageResult<SdkOrderRespVo> emptyResult = new PageResult<>();
            emptyResult.setList(new ArrayList<>());
            emptyResult.setTotal(0L);
            return emptyResult;
        }

        // 添加产品ID和渠道代码的组合条件
        List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> finalFilteredCombinations = filteredCombinations;
        query.and(wrapper -> {
            for (int i = 0; i < finalFilteredCombinations.size(); i++) {
                com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = finalFilteredCombinations.get(i);
                if (i == 0) {
                    wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                } else {
                    wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                }
            }
        });

        // 为了确保查询获取到所有关联信息，添加其他表连接
        query.select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on ->
                        on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o -> o.eq(SdkOrderDO::getProductId, SdkOptLinkDO::getProductId).eq(SdkOrderDO::getChannelCode, SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class, "o", SdkOptDO::getId, SdkOptLinkDO::getLinkId)
                .orderByDesc(SdkOrderDO::getId);

        // 执行分页查询
        PageResult<SdkOrderRespVo> resultPage = selectJoinPage(pageReqVO, SdkOrderRespVo.class, query);

        // 转换为自定义分页结果
        PageResult<SdkOrderRespVo> result = new PageResult<>();
        result.setList(resultPage.getList());
        result.setTotal(resultPage.getTotal());
        return result;
    }

    /**
     * 获取运营权限下的每日订单支付摘要，使用产品ID和渠道代码进行精确匹配
     *
     * @param pageReqVO  请求参数
     * @param conditions 产品ID和渠道代码的组合条件
     * @return 订单支付摘要信息
     */
    default SdkOrderSummaryRespVO getOperationOrderPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPrice)
                .selectCount(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPay)
                .eq(SdkOrderDO::getPayStatus, 1)
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select(SdkPaysDO::getPayName);

        // 添加产品ID和渠道代码的组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    } else {
                        wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }

            if (pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty()) {
                queryWrapper.in(SdkOrderDO::getChannelCode, pageReqVO.getChannelCode());
            }
        }

        return selectJoinOne(SdkOrderSummaryRespVO.class, queryWrapper);
    }

    /**
     * 获取运营权限下的每周订单支付摘要，使用产品ID和渠道代码进行精确匹配
     *
     * @param pageReqVO  请求参数
     * @param conditions 产品ID和渠道代码的组合条件
     * @return 订单支付摘要信息
     */
    default SdkOrderSummaryRespVO getOperationOrderPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getWeekPrice)
                .selectCount(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getWeekPay)
                .between(SdkOrderDO::getPayTime, startTime, endTime)  // 使用计算的周时间范围
                .eq(SdkOrderDO::getPayStatus, 1)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select(SdkPaysDO::getPayName);

        // 添加产品ID和渠道代码的组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    } else {
                        wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }

            if (pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty()) {
                queryWrapper.in(SdkOrderDO::getChannelCode, pageReqVO.getChannelCode());
            }
        }

        return selectJoinOne(SdkOrderSummaryRespVO.class, queryWrapper);
    }

    /**
     * 获取运营权限下的每日付费用户统计，使用产品ID和渠道代码进行精确匹配
     */
    SdkOrderSummaryRespVO getOperationUserPaySummaryByDay(@Param("payTime") Long[] payTime,
                                                          @Param("productId") List<Long> productId,
                                                          @Param("channelCode") List<String> channelCode,
                                                          @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 统计运营权限下一周内的付费用户数，使用产品ID和渠道代码进行精确匹配
     */
    Long countOperationWeekPayUsers(@Param("startTime") Long startTime,
                                    @Param("endTime") Long endTime,
                                    @Param("productId") List<Long> productId,
                                    @Param("channelCode") List<String> channelCode,
                                    @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 按天统计运营权限下的交易金额，使用产品ID和渠道代码进行精确匹配
     */
    List<SdkOrderSummaryMonthRespVO> getOperationDailyTradeAmount(@Param("startTime") Long startTime,
                                                                  @Param("endTime") Long endTime,
                                                                  @Param("productId") List<Long> productId,
                                                                  @Param("channelCode") List<String> channelCode,
                                                                  @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 按天统计运营权限下的订单数，使用产品ID和渠道代码进行精确匹配
     */
    List<SdkOrderSummaryMonthRespVO> getOperationDailyPay(@Param("startTime") Long startTime,
                                                          @Param("endTime") Long endTime,
                                                          @Param("productId") List<Long> productId,
                                                          @Param("channelCode") List<String> channelCode,
                                                          @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 按天统计运营权限下的付费用户数，使用产品ID和渠道代码进行精确匹配
     */

    List<SdkOrderSummaryMonthRespVO> getOperationDailyPayUsers(@Param("startTime") Long startTime,
                                                               @Param("endTime") Long endTime,
                                                               @Param("productId") List<Long> productId,
                                                               @Param("channelCode") List<String> channelCode,
                                                               @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 获取运营权限下的用户付费统计信息，使用产品ID和渠道代码进行精确匹配
     *
     * @param pageReqVO  请求参数
     * @param conditions 产品ID和渠道代码的关联条件
     * @return 用户付费统计信息
     */
    default SdkOrderSummaryRespVO getOperationUserPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        List<Long> productIds = null;
        List<String> channelCodes = null;

        // 如果没有条件列表，则使用请求中的产品ID和渠道代码
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();

            // 提取服务器名称
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                channelCodes = Collections.singletonList(pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                channelCodes = pageReqVO.getServers();
            }

            return getOperationUserPaySummaryByDay(pageReqVO.getPayTime(), productIds, channelCodes, null);
        } else {
            return getOperationUserPaySummaryByDay(pageReqVO.getPayTime(), null, null, conditions);
        }
    }

    /**
     * 获取运营权限下的每周用户付费统计信息，使用产品ID和渠道代码进行精确匹配
     *
     * @param pageReqVO  请求参数
     * @param conditions 产品ID和渠道代码的关联条件
     * @return 每周用户付费统计信息
     */
    default SdkOrderSummaryRespVO getOperationUserPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        List<Long> productIds = null;
        List<String> channelCodes = null;

        // 如果没有条件列表，则使用请求中的产品ID和渠道代码
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();

            // 提取服务器名称
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                channelCodes = Collections.singletonList(pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                channelCodes = pageReqVO.getServers();
            }

            Long weekPayUsers = countOperationWeekPayUsers(startTime, endTime, productIds, channelCodes, null);
            SdkOrderSummaryRespVO result = new SdkOrderSummaryRespVO();
            result.setWeekPayUser(weekPayUsers);
            return result;
        } else {
            Long weekPayUsers = countOperationWeekPayUsers(startTime, endTime, null, null, conditions);
            SdkOrderSummaryRespVO result = new SdkOrderSummaryRespVO();
            result.setWeekPayUser(weekPayUsers);
            return result;
        }
    }

    /**
     * 获取运营权限下的按月交易金额统计，使用产品ID和渠道代码进行精确匹配
     */
    default List<SdkOrderSummaryMonthRespVO> getOperationMonthlyTradeAmount(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        List<Long> productIds = null;
        List<String> channelCodes = null;

        // 处理条件
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();
            channelCodes = pageReqVO.getChannelCode();

            return getOperationDailyTradeAmount(startOfMonth, endOfMonth, productIds, channelCodes, null);
        } else {
            return getOperationDailyTradeAmount(startOfMonth, endOfMonth, null, null, conditions);
        }
    }

    /**
     * 获取运营权限下的按月订单数统计，使用产品ID和渠道代码进行精确匹配
     */
    default List<SdkOrderSummaryMonthRespVO> getOperationMonthlyPay(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        List<Long> productIds = null;
        List<String> channelCodes = null;

        // 处理条件
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();
            channelCodes = pageReqVO.getChannelCode();

            return getOperationDailyPay(startOfMonth, endOfMonth, productIds, channelCodes, null);
        } else {
            return getOperationDailyPay(startOfMonth, endOfMonth, null, null, conditions);
        }
    }

    /**
     * 获取运营权限下的按月付费用户数统计，使用产品ID和渠道代码进行精确匹配
     */
    default List<SdkOrderSummaryMonthRespVO> getOperationMonthlyPayUsers(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        List<Long> productIds = null;
        List<String> channelCodes = null;

        // 处理条件
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();
            channelCodes = pageReqVO.getChannelCode();

            return getOperationDailyPayUsers(startOfMonth, endOfMonth, productIds, channelCodes, null);
        } else {
            return getOperationDailyPayUsers(startOfMonth, endOfMonth, null, null, conditions);
        }
    }

    /**
     * 使用运营权限条件查询订单总金额
     *
     * @param pageReqVO  查询条件
     * @param conditions 产品ID和渠道代码的组合条件
     * @return 总金额结果
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmountWithOperationConditions(OrderPageReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            SdkOrderTotalAmountRespVo emptyResult = new SdkOrderTotalAmountRespVo();
            emptyResult.setTotalAmount(0F);
            return emptyResult;
        }

        // 构建查询条件
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderTotalAmountRespVo::getTotalAmount)
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getUid, pageReqVO.getUid())
                .inIfExists(SdkChannelDO::getChannelCode, pageReqVO.getChannelCode())
                .inIfExists(SdkProductDO::getId, pageReqVO.getProductId())
                .inIfExists(SdkOrderDO::getServerName, pageReqVO.getServers())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName())
                .leftJoin(SdkChannelDO.class, "channel", on -> on
                        .eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                        .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType);

        // 添加权限条件
        queryWrapper.and(wrapper -> {
            for (int i = 0; i < conditions.size(); i++) {
                com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = conditions.get(i);
                wrapper.or(orWrapper -> orWrapper
                        .eq(SdkOrderDO::getProductId, condition.getProductId())
                        .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
            }
        });

        // 执行查询
        SdkOrderTotalAmountRespVo result = selectJoinOne(SdkOrderTotalAmountRespVo.class, queryWrapper);
        return result != null ? result : new SdkOrderTotalAmountRespVo().setTotalAmount(0);
    }

    /**
     * 根据产品ID和渠道代码列表查询订单总金额
     * 此方法支持按照产品ID和渠道代码进行交叉查询
     *
     * @param pageReqVO    分页请求参数
     * @param productIds   有权限的产品ID列表
     * @param channelCodes 有权限的渠道代码列表
     * @param conditions   完整的权限条件列表
     * @return 订单总金额
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmountWithProductAndChannels(
            OrderPageReqVO pageReqVO,
            List<Long> productIds,
            List<String> channelCodes,
            List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {

        // 构建查询条件
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderTotalAmountRespVo::getTotalAmount)
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getUid, pageReqVO.getUid())
                .inIfExists(SdkOrderDO::getServerName, pageReqVO.getServers())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName())
                .leftJoin(SdkChannelDO.class, "channel", on -> on
                        .eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                        .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType);

        // 处理用户指定的产品ID和渠道代码
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();

        // 如果用户指定了产品ID，则需要与权限范围内的产品ID取交集
        if (hasUserProductId) {
            List<Long> filteredProductIds = pageReqVO.getProductId().stream()
                    .filter(productIds::contains)
                    .collect(Collectors.toList());

            if (filteredProductIds.isEmpty()) {
                return new SdkOrderTotalAmountRespVo().setTotalAmount(0F);
            }

            queryWrapper.in(SdkOrderDO::getProductId, filteredProductIds);
        } else {
            // 否则使用所有有权限的产品ID
            queryWrapper.in(SdkOrderDO::getProductId, productIds);
        }

        // 如果用户指定了渠道代码，则需要与权限范围内的渠道代码取交集
        if (hasUserChannelCode) {
            List<String> filteredChannelCodes = pageReqVO.getChannelCode().stream()
                    .filter(channelCodes::contains)
                    .collect(Collectors.toList());

            if (filteredChannelCodes.isEmpty()) {
                return new SdkOrderTotalAmountRespVo().setTotalAmount(0F);
            }

            queryWrapper.in(SdkOrderDO::getChannelCode, filteredChannelCodes);
        } else {
            // 否则使用所有有权限的渠道代码
            queryWrapper.in(SdkOrderDO::getChannelCode, channelCodes);
        }

        // 添加精确的产品ID和渠道代码组合条件
        if (!CollectionUtils.isEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = conditions.get(i);

                    // 如果用户指定了产品ID和渠道代码，则需要验证是否在权限范围内
                    if (hasUserProductId && hasUserChannelCode) {
                        if (pageReqVO.getProductId().contains(condition.getProductId()) &&
                                pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            wrapper.or(orWrapper -> orWrapper
                                    .eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                    // 如果用户只指定了产品ID
                    else if (hasUserProductId) {
                        if (pageReqVO.getProductId().contains(condition.getProductId())) {
                            wrapper.or(orWrapper -> orWrapper
                                    .eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                    // 如果用户只指定了渠道代码
                    else if (hasUserChannelCode) {
                        if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            wrapper.or(orWrapper -> orWrapper
                                    .eq(SdkOrderDO::getProductId, condition.getProductId())
                                    .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                    // 如果用户没有指定产品ID和渠道代码
                    else {
                        wrapper.or(orWrapper -> orWrapper
                                .eq(SdkOrderDO::getProductId, condition.getProductId())
                                .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                    }
                }
            });
        }

        // 执行查询
        SdkOrderTotalAmountRespVo result = selectJoinOne(SdkOrderTotalAmountRespVo.class, queryWrapper);
        return result != null ? result : new SdkOrderTotalAmountRespVo().setTotalAmount(0F);
    }

    IPage<ServerPageRespVO> selectPageByServer(IPage<ServerPageRespVO> page, @Param("reqVO") ServerPageReqVO pageReqVO);

    List<ServerChannelDetailsRespVO> selectChannelDetails(@Param("reqVO") ServerPageReqVO pageReqVO);


    IPage<SdkOrderRespVo> selectPageByLeaderGroup(IPage<OrderPageReqVO> page, @Param("userId") Long userId, @Param("reqVO") OrderPageReqVO reqVO);

    SdkOrderTotalAmountRespVo selectPageByLeaderGroupTotalPage(@Param("userId") Long userId, @Param("reqVO") OrderPageReqVO reqVO);
}