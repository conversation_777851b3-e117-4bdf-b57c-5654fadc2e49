package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 支付配置新增/修改 Request VO")
@Data
public class PayConfigSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝支付")
    @NotEmpty(message = "配置名称不能为空")
    private String name;

    @Schema(description = "支付提供商", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    @NotEmpty(message = "支付提供商不能为空")
    private String provider;

    @Schema(description = "图标地址", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "备注", example = "支付宝支付配置")
    private String remark;

    @Schema(description = "配置参数列表")
    private List<PayConfigParamSaveReqVO> params;

    @Schema(description = "支付方式列表")
    private List<PayConfigMethodSaveReqVO> methods;

}
