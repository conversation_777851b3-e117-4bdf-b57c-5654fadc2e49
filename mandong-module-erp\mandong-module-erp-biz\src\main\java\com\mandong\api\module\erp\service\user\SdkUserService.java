package com.mandong.api.module.erp.service.user;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

public interface SdkUserService {

    PageResult<UserPageRespVO> getUserPage(UserPageReqVO pageReqVO);

    UserDetailRespVO getUserDetail(Long id);

    PageResult<UserDevicePageRespVO> getUserDevicePage(UserPageReqVO pageReqVO);

    PageResult<UserLoginPageRespVO> getUserLoginPage(UserPageReqVO pageReqVO);

    CommonResult transferChannel(SdkUserTransferReqVO reqVO);

    void updateStatus(Long uid,Integer status);
}
