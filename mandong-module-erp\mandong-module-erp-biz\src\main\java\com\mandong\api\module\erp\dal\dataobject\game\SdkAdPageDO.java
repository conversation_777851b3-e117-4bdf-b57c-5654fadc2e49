package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_ad_page")
public class SdkAdPageDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "productId")
    private Integer productId;

    @TableField(value = "`name`")
    private String name;

    @TableField(value = "templateId")
    private Boolean templateId;

    @TableField(value = "`style`")
    private String style;

    @TableField(value = "createTime")
    private Integer createTime;

    @TableField(value = "updateTime")
    private Integer updateTime;

    @TableField(value = "clickNum")
    private Integer clickNum;

    @TableField(value = "downNum")
    private Integer downNum;

    /**
     * 1未审核
     */
    @TableField(value = "nowStatus")
    private Byte nowStatus;
}