<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>mandong</artifactId>
        <groupId>com.mandong.api</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>mandong-common</module>
        <module>mandong-spring-boot-starter-mybatis</module>
        <module>mandong-spring-boot-starter-redis</module>
        <module>mandong-spring-boot-starter-web</module>
        <module>mandong-spring-boot-starter-security</module>
        <module>mandong-spring-boot-starter-websocket</module>

        <module>mandong-spring-boot-starter-monitor</module>
        <module>mandong-spring-boot-starter-protection</module>
        <module>mandong-spring-boot-starter-job</module>
        <module>mandong-spring-boot-starter-mq</module>

        <module>mandong-spring-boot-starter-excel</module>
        <module>mandong-spring-boot-starter-test</module>

        <module>mandong-spring-boot-starter-biz-tenant</module>
        <module>mandong-spring-boot-starter-biz-data-permission</module>
        <module>mandong-spring-boot-starter-biz-ip</module>
    </modules>

    <artifactId>mandong-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

</project>
