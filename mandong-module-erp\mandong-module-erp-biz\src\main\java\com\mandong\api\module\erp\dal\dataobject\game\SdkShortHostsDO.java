package com.mandong.api.module.erp.dal.dataobject.game;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_short_hosts")
public class SdkShortHostsDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "manageUid")
    private Integer manageUid;

    @TableField(value = "`host`")
    private String host;

    /**
     * 是否有备案 0无 1有
     */
    @TableField(value = "hasIcp")
    private Boolean hasIcp;

    /**
     * 备注
     */
    @TableField(value = "note")
    private String note;
}