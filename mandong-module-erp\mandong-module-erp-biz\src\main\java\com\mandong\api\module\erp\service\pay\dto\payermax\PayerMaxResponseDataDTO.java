package com.mandong.api.module.erp.service.pay.dto.payermax;

import lombok.Data;

/**
 * PayerMax支付响应数据DTO
 *
 * <AUTHOR>
 */
@Data
public class PayerMaxResponseDataDTO {
    
    /**
     * PayerMax订单号
     */
    private String tradeToken;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 支付链接
     */
    private String redirectUrl;

    /**
     * 订单状态
     * SUCCESS 交易成功
     * PENDING 等待付款
     * FAILED 交易失败
     * CLOSED 交易关单
     */
    private String status;

    

}
