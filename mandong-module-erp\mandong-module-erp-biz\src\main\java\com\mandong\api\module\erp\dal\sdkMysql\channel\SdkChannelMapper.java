package com.mandong.api.module.erp.dal.sdkMysql.channel;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.hutool.core.collection.CollUtil;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import org.springframework.util.StringUtils;
import com.mandong.api.framework.common.pojo.PageParam;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.util.MyBatisUtils;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.ChannelGetListRespVO;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.SdkChannelVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkChannelMapper extends BaseMapperX<SdkChannelDO> {

    default PageResult<SdkChannelVO> selectPage(ChannelGetListRespVO channelGetListRespVO){
        return selectJoinPage(channelGetListRespVO, SdkChannelVO.class,new MPJLambdaWrapperX<SdkChannelDO>()
                .selectAll(SdkChannelDO.class)
                .inIfPresent(SdkChannelDO::getProductId, channelGetListRespVO.getProductId())
                .likeIfPresent(SdkChannelDO::getChannelName, channelGetListRespVO.getChannelName())
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class,SdkProductDO::getId,SdkChannelDO::getProductId)

                .orderByDesc(SdkChannelDO::getId)

        );
    }
}