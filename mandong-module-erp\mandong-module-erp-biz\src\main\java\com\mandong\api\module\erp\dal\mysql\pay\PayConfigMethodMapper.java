package com.mandong.api.module.erp.dal.mysql.pay;

import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigMethodDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支付配置方式 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayConfigMethodMapper extends BaseMapperX<PayConfigMethodDO> {

    default List<PayConfigMethodDO> selectListByConfigId(Long configId) {
        return selectList(new LambdaQueryWrapperX<PayConfigMethodDO>()
                .eq(PayConfigMethodDO::getConfigId, configId));
    }

    default void deleteByConfigId(Long configId) {
        delete(new LambdaQueryWrapperX<PayConfigMethodDO>()
                .eq(PayConfigMethodDO::getConfigId, configId));
    }

}