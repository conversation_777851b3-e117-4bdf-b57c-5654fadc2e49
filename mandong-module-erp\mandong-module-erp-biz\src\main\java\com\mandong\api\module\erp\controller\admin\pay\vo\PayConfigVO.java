package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 支付配置 Response VO")
@Data
public class PayConfigVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝支付")
    private String name;

    @Schema(description = "支付提供商", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    private String provider;

    @Schema(description = "图标地址", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "备注", example = "支付宝支付配置")
    private String remark;

    @Schema(description = "配置参数列表")
    private List<PayConfigParamVO> params;

    @Schema(description = "支付方式列表")
    private List<PayConfigMethodVO> methods;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

}
