package com.mandong.api.module.erp.dal.dataobject.finance;

import com.mandong.api.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * ERP 结算账户 DO
 *
 * <AUTHOR>
 */
@TableName("erp_account")
@KeySequence("erp_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpAccountDO extends BaseDO {

    /**
     * 结算账户编号
     */
    @TableId
    private Long id;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 账户编码
     */
    private String no;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开启状态
     *
     * 枚举 {@link com.mandong.api.framework.common.enums.CommonStatusEnum}
     */
    private Integer status;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 是否默认
     */
    private Boolean defaultStatus;

}