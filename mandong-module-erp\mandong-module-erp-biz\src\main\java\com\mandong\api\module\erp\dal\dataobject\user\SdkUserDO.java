package com.mandong.api.module.erp.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_user")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkUserDO {

    @TableId
    private Long uid;
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    /**
     * 密码
     */
    @TableField("password")
    private String password;

    @TableField("slat")
    private String slat;
    /**
     *  游戏id
     */
    @TableField("productId")
    private Long productId;
    /**
     * 渠道code
     */
    @TableField("channelCode")
    private String channelCode;
    /**
     * 是否游客 0普通用户 1游客用户
     */
    @TableField("isGuest")
    private int isGuest;
    /**
     * 0正常 1禁用
     */
    @TableField("userStatus")
    private int userStatus;

    /**
     * 0正常 1禁用
     */
    @TableField("regTime")
    private Long regTime;


}
