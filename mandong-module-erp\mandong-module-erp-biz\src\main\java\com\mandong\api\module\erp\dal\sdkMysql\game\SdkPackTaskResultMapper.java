package com.mandong.api.module.erp.dal.sdkMysql.game;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackGameApkVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackResultRespVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskResultDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("sdkDB")
public interface SdkPackTaskResultMapper extends BaseMapperX<SdkPackTaskResultDO> {

    default  List<SdkPackResultRespVO> getResult(Long id){
        return selectJoinList(SdkPackResultRespVO.class,new MPJLambdaWrapperX<SdkPackTaskResultDO>()
                .select(SdkPackTaskResultDO::getTaskId,SdkPackTaskResultDO::getChannelCode,SdkPackTaskResultDO::getApkUrl,SdkPackTaskResultDO::getGameVersionId,SdkPackTaskResultDO::getShortLinkId)
                .eq(SdkPackTaskResultDO::getTaskId, id)
                .leftJoin(SdkPackTaskDO.class,SdkPackTaskDO::getId,SdkPackTaskResultDO::getTaskId)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on -> on.eq(SdkChannelDO::getProductId, SdkPackTaskDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkPackTaskResultDO::getChannelCode))
                .select(SdkShortLinkDO::getUseAdPage)
                .leftJoin(SdkShortLinkDO.class,SdkShortLinkDO::getId,SdkPackTaskResultDO::getShortLinkId)

        );
    }
}