package com.mandong.api.module.erp.framework.web.config;

import com.mandong.api.framework.swagger.config.MandongSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * erp 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class ErpWebConfiguration {

    /**
     * erp 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi erpGroupedOpenApi() {
        return MandongSwaggerAutoConfiguration.buildGroupedOpenApi("erp");
    }

}
