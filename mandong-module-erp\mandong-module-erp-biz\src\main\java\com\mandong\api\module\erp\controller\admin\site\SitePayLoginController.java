package com.mandong.api.module.erp.controller.admin.site;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.site.vo.*;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLogin;
import com.mandong.api.module.erp.service.site.SitePayLoginService;
import com.mandong.api.module.erp.service.site.SitePayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点管理-支付网站管理")
@RestController
@RequestMapping("/site/pay-login")
@Validated
public class SitePayLoginController {

    @Resource
    SitePayLoginService sitePayLoginService;



    @GetMapping("/list")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<List<SitePayLogin>> getList(@RequestParam("sitePayId") Long sitePayId) {

        return success(sitePayLoginService.getList(sitePayId));
    }


    @GetMapping("/get")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:query')")
    public CommonResult<SitePayLoginVO> get(@RequestParam("id") Long id) {

        return success(sitePayLoginService.get(id));
    }


    @PostMapping("/create")
    @Operation(summary = "新增支付网站登录方式")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:add')")
    public CommonResult<Integer> create(@Valid @RequestBody SitePayLoginVO reqVO) {

        return success(sitePayLoginService.create(reqVO));
    }

    @GetMapping("/delete")
    @Operation(summary = "删除支付网站登录方式")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {

        return success(sitePayLoginService.delete(id));
    }



    @PostMapping("/update")
    @Operation(summary = "修改支付网站")
    @PreAuthorize("@ss.hasAnyPermissions('site:pay:edit')")
    public CommonResult<Integer> update(@Valid @RequestBody SitePayLoginVO pageReqVO) {
        return success(sitePayLoginService.update(pageReqVO));
    }





}
