package com.mandong.api.module.erp.service.pay.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * PayerMax签名工具类
 * 
 * 签名算法规范：
 * - 算法：RSA
 * - Key格式：PKCS8
 * - 签名算法：SHA256WithRSA
 * - 密钥长度：2048
 *
 * <AUTHOR>
 */
@Slf4j
public class PayerMaxSignatureUtil {
    
    private static final String ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    
    /**
     * 生成PayerMax签名
     * 
     * 注意：使用商户私钥进行加签的报文字符串要保证和http body中的字符串是一致的，
     * 否则会导致payermax验签不通过。
     * 
     * @param data 待签名的数据（JSON字符串）
     * @param privateKeyStr 商户私钥（PKCS8格式，Base64编码）
     * @return 签名字符串（Base64编码）
     * @throws Exception 签名异常
     */
    public static String generateSignature(String data, String privateKeyStr) throws Exception {
        if (StrUtil.isBlank(data) || StrUtil.isBlank(privateKeyStr)) {
            throw new IllegalArgumentException("签名数据和私钥不能为空");
        }
        
        try {
            // 1. 清理私钥字符串，移除PEM格式的头尾标识
            String cleanPrivateKey = cleanKeyString(privateKeyStr, "PRIVATE KEY");
            
            // 2. 解码私钥
            byte[] keyBytes = Base64.decode(cleanPrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
            
            // 3. 创建签名对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            
            // 4. 生成签名并Base64编码
            byte[] signBytes = signature.sign();
            String signResult = Base64.encode(signBytes);
            
            log.debug("PayerMax签名生成成功，数据长度：{}，签名长度：{}", data.length(), signResult.length());
            return signResult;
            
        } catch (Exception e) {
            log.error("生成PayerMax签名失败，数据：{}", data, e);
            throw new RuntimeException("签名生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证PayerMax签名
     * 
     * @param data 原始数据
     * @param signStr 签名字符串（Base64编码）
     * @param publicKeyStr 公钥字符串（X509格式，Base64编码）
     * @return 验证结果
     */
    public static boolean verifySignature(String data, String signStr, String publicKeyStr) {
        if (StrUtil.isBlank(data) || StrUtil.isBlank(signStr)) {
            log.warn("验签数据或签名为空");
            return false;
        }
        
        if (StrUtil.isBlank(publicKeyStr)) {
            log.warn("公钥为空，跳过签名验证");
            return true; // 如果没有配置公钥，则跳过验证
        }
        
        try {
            // 1. 清理公钥字符串
            String cleanPublicKey = cleanKeyString(publicKeyStr, "PUBLIC KEY");
            
            // 2. 解码公钥
            byte[] keyBytes = Base64.decode(cleanPublicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            
            // 3. 创建验证对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            
            // 4. 验证签名
            byte[] signBytes = Base64.decode(signStr);
            boolean result = signature.verify(signBytes);
            
            log.debug("PayerMax签名验证结果：{}，数据长度：{}，签名长度：{}", 
                     result, data.length(), signStr.length());
            return result;
            
        } catch (Exception e) {
            log.error("验证PayerMax签名失败，数据：{}，签名：{}", data, signStr, e);
            return false;
        }
    }
    
    /**
     * 清理密钥字符串，移除PEM格式的头尾标识和空白字符
     * 
     * @param keyStr 原始密钥字符串
     * @param keyType 密钥类型（"PRIVATE KEY" 或 "PUBLIC KEY"）
     * @return 清理后的密钥字符串
     */
    private static String cleanKeyString(String keyStr, String keyType) {
        return keyStr
                .replace("-----BEGIN " + keyType + "-----", "")
                .replace("-----END " + keyType + "-----", "")
                .replaceAll("\\s+", ""); // 移除所有空白字符（空格、换行、制表符等）
    }
    
    /**
     * 验证密钥格式是否正确
     * 
     * @param privateKeyStr 私钥字符串
     * @param publicKeyStr 公钥字符串
     * @return 验证结果
     */
    public static boolean validateKeyFormat(String privateKeyStr, String publicKeyStr) {
        try {
            // 测试私钥
            if (StrUtil.isNotBlank(privateKeyStr)) {
                String cleanPrivateKey = cleanKeyString(privateKeyStr, "PRIVATE KEY");
                byte[] privateKeyBytes = Base64.decode(cleanPrivateKey);
                PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
                KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
                keyFactory.generatePrivate(privateKeySpec);
            }
            
            // 测试公钥
            if (StrUtil.isNotBlank(publicKeyStr)) {
                String cleanPublicKey = cleanKeyString(publicKeyStr, "PUBLIC KEY");
                byte[] publicKeyBytes = Base64.decode(cleanPublicKey);
                X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyBytes);
                KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
                keyFactory.generatePublic(publicKeySpec);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("密钥格式验证失败", e);
            return false;
        }
    }
    
    /**
     * 生成测试用的签名示例
     * 
     * @param testData 测试数据
     * @param privateKeyStr 私钥
     * @return 签名结果
     */
    public static String generateTestSignature(String testData, String privateKeyStr) {
        try {
            return generateSignature(testData, privateKeyStr);
        } catch (Exception e) {
            log.error("生成测试签名失败", e);
            return null;
        }
    }
}
