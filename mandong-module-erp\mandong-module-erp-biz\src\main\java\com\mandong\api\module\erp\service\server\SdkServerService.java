package com.mandong.api.module.erp.service.server;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerChannelDetailsRespVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageReqVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import jakarta.validation.Valid;

import java.util.List;

public interface SdkServerService {

    PageResult<ServerPageRespVO> getPage(ServerPageReqVO pageReqVO);

    List<ServerChannelDetailsRespVO> getChannelDetails(ServerPageReqVO pageReqVO);
}
