package com.mandong.api.module.erp.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * SDK订单条件 VO
 * 
 * <AUTHOR>
 */
@Schema(description = "SDK订单产品和区服条件")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SdkOrderCondition {
    
    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long productId;
    
    /**
     * 区服名称
     */
    @Schema(description = "区服名称")
    private String serverName;

    private String channelCode;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SdkOrderCondition that = (SdkOrderCondition) o;
        return Objects.equals(productId, that.productId) && Objects.equals(serverName, that.serverName) && Objects.equals(channelCode, that.channelCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(productId, serverName,channelCode);
    }
} 