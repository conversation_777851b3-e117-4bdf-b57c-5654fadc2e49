package com.mandong.api.module.erp.dal.dataobject.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 支付配置参数表
 */
@Data
@TableName(value = "pay_config_param")
public class PayConfigParamDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付配置ID
     */
    @TableField(value = "config_id")
    private Long configId;

    /**
     * 参数键
     */
    @TableField(value = "param_key")
    private String paramKey;

    /**
     * 参数值
     */
    @TableField(value = "param_value")
    private String paramValue;

    /**
     * 参数类型(STRING, NUMBER, BOOLEAN, JSON)
     */
    @TableField(value = "param_type")
    private String paramType;

    /**
     * 是否加密存储(0:否 1:是)
     */
    @TableField(value = "is_encrypted")
    private Boolean isEncrypted;

    /**
     * 参数描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
}