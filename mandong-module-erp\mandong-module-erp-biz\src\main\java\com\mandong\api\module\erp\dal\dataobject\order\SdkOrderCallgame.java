package com.mandong.api.module.erp.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_order_callgame")
public class SdkOrderCallgame {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "orderNo")
    private String orderno;

    @TableField(value = "createTime")
    private Long createtime;

    @TableField(value = "callParams")
    private String callparams;

    /**
     * 原始回调结果
     */
    @TableField(value = "`status`")
    private String status;

    @TableField(value = "callUrl")
    private String callurl;
}