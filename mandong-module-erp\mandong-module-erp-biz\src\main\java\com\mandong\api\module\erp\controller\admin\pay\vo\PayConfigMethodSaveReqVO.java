package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 支付配置方式保存 Request VO")
@Data
public class PayConfigMethodSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "配置编号", example = "1")
    private Long configId;

    @Schema(description = "支付方式编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "支付方式编号不能为空")
    private Long methodId;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "排序", example = "1")
    private Integer sort;

}
