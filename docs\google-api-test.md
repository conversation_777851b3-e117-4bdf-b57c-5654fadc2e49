# Google API 测试指南

## 1. 手动测试Google OAuth流程

### 1.1 获取授权码

在浏览器中访问以下URL（替换YOUR_CLIENT_ID）：

```
https://accounts.google.com/o/oauth2/v2/auth?client_id=YOUR_CLIENT_ID&redirect_uri=http://localhost:3001/auth/google/callback&response_type=code&scope=openid email profile&state=test123
```

### 1.2 测试Token API

使用curl测试获取访问令牌：

```bash
curl -X POST https://oauth2.googleapis.com/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=YOUR_CLIENT_ID" \
  -d "client_secret=YOUR_CLIENT_SECRET" \
  -d "code=YOUR_AUTHORIZATION_CODE" \
  -d "grant_type=authorization_code" \
  -d "redirect_uri=http://localhost:3001/auth/google/callback"
```

响应示例：
```json
{
  "access_token": "ya29.a0AfH6SMC...",
  "expires_in": 3599,
  "token_type": "Bearer",
  "scope": "openid email profile",
  "id_token": "eyJhbGciOiJSUzI1NiIs..."
}
```

### 1.3 测试UserInfo API

使用访问令牌获取用户信息：

```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  https://www.googleapis.com/oauth2/v2/userinfo
```

响应示例：
```json
{
  "id": "123456789012345678901",
  "email": "<EMAIL>",
  "verified_email": true,
  "name": "John Doe",
  "given_name": "John",
  "family_name": "Doe",
  "picture": "https://lh3.googleusercontent.com/...",
  "locale": "en"
}
```

## 2. 应用程序测试

### 2.1 配置测试环境

在 `application-local.yaml` 中配置：

```yaml
google:
  oauth:
    client-id: "your-actual-google-client-id"
    client-secret: "your-actual-google-client-secret"
    redirect-uri: "http://localhost:3001/auth/google/callback"
```

### 2.2 测试登录接口

```bash
curl -X POST http://localhost:48080/admin-api/erp/game/google-login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "your-authorization-code",
    "gameId": "game123",
    "productId": "product123"
  }'
```

### 2.3 预期响应

成功响应：
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "uid": 12345,
    "username": "<EMAIL>",
    "productName": "产品名称",
    "productId": 123,
    "channelName": "渠道名称",
    "isGuest": 0,
    "userStatus": 1,
    "regTime": 1640995200000,
    "totalAmount": 0.0,
    "loginTotal": 1,
    "deviceId": "设备ID",
    "callbackUrl": "回调URL",
    "callbackKey": "回调密钥",
    "productCode": "产品代码"
  }
}
```

失败响应：
```json
{
  "code": 500,
  "msg": "登录失败",
  "data": null
}
```

## 3. 常见问题排查

### 3.1 授权码无效
- 检查授权码是否已过期（通常5-10分钟）
- 确认redirect_uri与Google Console配置一致
- 验证client_id和client_secret是否正确

### 3.2 用户不存在
- 确认系统中存在对应的用户记录
- 检查用户名是否与Google email或ID匹配
- 验证productId是否正确

### 3.3 API调用失败
- 检查网络连接
- 验证Google API是否可访问
- 查看服务器日志获取详细错误信息

## 4. 调试技巧

### 4.1 启用详细日志

在 `application-local.yaml` 中添加：

```yaml
logging:
  level:
    com.mandong.api.module.erp.service.game.GameServiceImpl: DEBUG
    org.springframework.web.client.RestTemplate: DEBUG
```

### 4.2 使用Postman测试

1. 创建新的POST请求到 `/admin-api/erp/game/google-login`
2. 设置Content-Type为application/json
3. 在Body中添加测试数据
4. 发送请求并查看响应

### 4.3 前端调试

使用浏览器开发者工具：
1. 检查Network标签页中的API请求
2. 查看Console中的错误信息
3. 验证授权码是否正确获取

## 5. 性能优化建议

1. **连接池配置**: 为RestTemplate配置连接池
2. **超时设置**: 设置合理的连接和读取超时时间
3. **缓存策略**: 考虑缓存Google用户信息（短期）
4. **异步处理**: 对于非关键路径，考虑异步处理
