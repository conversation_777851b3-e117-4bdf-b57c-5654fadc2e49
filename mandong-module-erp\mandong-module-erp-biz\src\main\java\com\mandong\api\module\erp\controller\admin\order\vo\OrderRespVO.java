package com.mandong.api.module.erp.controller.admin.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.mandong.api.framework.excel.core.annotations.DictFormat;
import com.mandong.api.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2452")
    @ExcelProperty("主键")
    private Integer id;

    @Schema(description = "用户名", example = "赵六")
    @ExcelProperty("用户名")
    private String userName;

    @Schema(description = "用户账号", example = "22173")
    @ExcelProperty("用户账号")
    private String userAccount;

    @Schema(description = "部门")
    @ExcelProperty("部门")
    private String department;

    @Schema(description = "推广")
    @ExcelProperty("推广")
    private String promotion;

    @Schema(description = "游戏名", example = "芋艿")
    @ExcelProperty("游戏名")
    private String gameName;

    @Schema(description = "角色id", example = "22926")
    @ExcelProperty("角色id")
    private String userId;

    @Schema(description = "区服")
    @ExcelProperty("区服")
    private String service;

    @Schema(description = "角色名", example = "李四")
    @ExcelProperty("角色名")
    private String roleName;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "金额（美元）")
    @ExcelProperty("金额（美元）")
    private Float amountUsd;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "金额")
    @ExcelProperty("金额")
    private Float amount;

    @Schema(description = "商品")
    @ExcelProperty("商品")
    private String goods;

    @Schema(description = "支付方式")
    @ExcelProperty("支付方式")
    private String payment;

    @Schema(description = "交易时间")
    @ExcelProperty("交易时间")
    private LocalDateTime payTime;

    @Schema(description = "交易号")
    @ExcelProperty("交易号")
    private String transactionNumber;

    @Schema(description = "订单号")
    @ExcelProperty("订单号")
    private String orderNumber;

    @Schema(description = "带队归属")
    @ExcelProperty("带队归属")
    private String teamBelong;

    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private LocalDateTime registrationTime;

    @Schema(description = "订单状态", example = "1")
    @ExcelProperty("订单状态")
    private String orderStatus;

    @Schema(description = "游戏单号")
    @ExcelProperty("游戏单号")
    private String gameOrderNumber;

    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime completionTime;

    @Schema(description = "用户ip")
    @ExcelProperty("用户ip")
    private String userIp;

    @Schema(description = "订单来源（1--龙成后台  2---sdk后台)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "订单来源（1--龙成后台  2---sdk后台)", converter = DictConvert.class)
    @DictFormat("erp_order_source") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer orderSource;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}