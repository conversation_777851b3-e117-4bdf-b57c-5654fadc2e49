package com.mandong.api.module.erp.dal.sdkMysql.opt;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupPageReqVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupRespVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptPageReqVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptRespVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptVO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupDO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkOptMapper extends BaseMapperX<SdkOptDO> {




    default PageResult<OptRespVO> selectPage(OptPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, OptRespVO.class, new MPJLambdaWrapperX<SdkOptDO>()
                .eqIfPresent(SdkOptDO::getUserName, pageReqVO.getUserName())
                .eqIfPresent(SdkOptDO::getGroupName, pageReqVO.getGroupName())
                .eqIfPresent(SdkOptDO::getUid, pageReqVO.getUid())
                .orderByDesc(SdkOptDO::getCreateTime)
        );
    }

    default List<OptVO> selectPage(Long uid) {
        return selectJoinList(OptVO.class, new MPJLambdaWrapperX<SdkOptDO>()
                .eqIfPresent(SdkOptDO::getUid, uid)
                .selectAll(SdkOptDO.class)
                .selectCollection(SdkOptLinkDO.class, OptVO::getLinks)
                .leftJoin(SdkOptLinkDO.class, on -> on.eq(SdkOptLinkDO::getLinkId, SdkOptDO::getId))
                .orderByDesc(SdkOptDO::getCreateTime)
        );
    }

    default List<SdkOptLinkDO> selectLinks(Long uid) {
        return selectJoinList(SdkOptLinkDO.class, new MPJLambdaWrapperX<SdkOptDO>()
                .selectAll(SdkOptLinkDO.class)
                .eqIfPresent(SdkOptDO::getUid, uid)
                .leftJoin(SdkOptLinkDO.class, on -> on.eq(SdkOptLinkDO::getLinkId, SdkOptDO::getId))

        );
    }

    /**
     * 获取运营人员的权限配置
     *
     * @param uid 用户ID
     * @return 运营人员产品和渠道的权限列表
     */
    List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationPermissionVO> selectOperationPermission(Long uid);



}