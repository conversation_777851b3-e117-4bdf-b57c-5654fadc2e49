package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 支付方式 Response VO")
@Data
public class PayMethodVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;
    private String type;
    @Schema(description = "支付方式编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "alipay")
    private String code;

    @Schema(description = "支付方式名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝")
    private String name;

    @Schema(description = "支付提供商", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    private String provider;

    @Schema(description = "图标地址", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "描述", example = "支付宝支付方式")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

}
