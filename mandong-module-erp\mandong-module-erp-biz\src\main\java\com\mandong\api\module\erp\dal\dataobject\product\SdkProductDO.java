package com.mandong.api.module.erp.dal.dataobject.product;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_product")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkProductDO {

    private Long id;
    /**
     * 游戏编码
     */
    @TableField("productCode")
    private String productCode;
    /**
     * 游戏名
     */
    @TableField("productName")
    private String productName;
    /**
     * 包名
     */
    @TableField("packageName")
    private String packageName;

    @TableField("callbackKey")
    private String callbackKey;

    @TableField("callbackUrl")
    private String callbackUrl;
    /**
     * 游戏平台：1 安卓  2 ios
     */
    @TableField("platform")
    private int platform;

    @TableField("createTime")
    private Long createTime;

    @TableField("gamePid")
    private String gamePid;

}
