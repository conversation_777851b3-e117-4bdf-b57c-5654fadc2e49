package com.mandong.api.module.erp.service.site;

import com.mandong.api.module.erp.controller.admin.site.vo.SitePayLoginVO;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLogin;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SitePayLoginService {
    List<SitePayLogin> getList( Long sitePayId);
    SitePayLoginVO get(Long id);
    Integer create( SitePayLoginVO reqVO);
    Integer delete( Long id);
    Integer update( SitePayLoginVO reqVO);

}
