package com.mandong.api.module.erp.controller.admin.game.vo;

import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class GameVersionRespVO {

    private Long id;

    private Long productId;

    private String taskName;

    private String note;
    private String versionName;
    private String channelCode;
    private String channelName;
    private String productName;
    private String apkUrl;
    private Integer nowStatus;
    private String messageTips;
    private Long runTime;
    private Long endTime;

}
