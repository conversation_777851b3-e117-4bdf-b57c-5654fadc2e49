<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 查询运营人员的产品和渠道权限 -->
    <select id="selectOperationPermission" resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationPermissionVO">
        SELECT 
            p.id AS productId,
            p.productName AS productName,
            c.channelCode AS channelCode,
            c.channelName AS channelName
        FROM 
            qsdk_opt o
        INNER JOIN 
            qsdk_opt_link ol ON o.id = ol.link_id
        INNER JOIN 
            qsdk_product p ON ol.product_id = p.id
        INNER JOIN 
            qsdk_channel c ON ol.channel_code = c.channelCode AND p.id = c.productId
        WHERE 
            o.uid = #{uid}

    </select>

</mapper>