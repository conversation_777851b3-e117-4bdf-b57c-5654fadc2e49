package com.mandong.api.module.erp.dal.mysql.site;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.site.vo.SitePaySkuRespVO;
import com.mandong.api.module.erp.dal.dataobject.site.SitePaySku;
import com.mandong.api.module.erp.dal.dataobject.site.SitePaySkuI18n;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SitePaySkuMapper extends BaseMapperX<SitePaySku> {

    default List<SitePaySkuRespVO> getSkuList(Long id){
        return selectJoinList(SitePaySkuRespVO.class,new MPJLambdaWrapperX<SitePaySku>()
                .selectAll(SitePaySku.class)
                .eqIfPresent(SitePaySku::getSiteId, id)
                .selectAll(SitePaySkuI18n.class)
                .leftJoin(SitePaySkuI18n.class ,SitePaySkuI18n::getSkuId,SitePaySku::getId)
                .selectCollection(SitePaySkuI18n.class,SitePaySkuRespVO::getI18ns)
        );
    }
}