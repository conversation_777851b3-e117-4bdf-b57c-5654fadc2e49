# PayerMax签名功能完善说明

## 概述

已完善PayerMax支付接口的签名功能，实现了完整的RSA SHA256WithRSA签名算法，确保与PayerMax API的兼容性。

## 签名算法规范

- **算法**: RSA
- **Key格式**: PKCS8 (私钥) / X509 (公钥)
- **签名算法**: SHA256WithRSA
- **密钥长度**: 2048位
- **编码格式**: Base64

## 核心功能

### 1. 签名工具类 `PayerMaxSignatureUtil`

提供了完整的签名生成和验证功能：

```java
// 生成签名
String signature = PayerMaxSignatureUtil.generateSignature(jsonData, privateKey);

// 验证签名
boolean isValid = PayerMaxSignatureUtil.verifySignature(data, signature, publicKey);

// 验证密钥格式
boolean isValidFormat = PayerMaxSignatureUtil.validateKeyFormat(privateKey, publicKey);
```

### 2. 集成到支付流程

在 `PaymentServiceImpl.callPayerMaxAPI()` 方法中：

```java
// 1. 生成请求JSON字符串（保持格式一致性）
String requestJson = JSONUtil.toJsonStr(request);

// 2. 生成签名
String privateKey = configParams.get("privateKey");
String sign = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);

// 3. 构建请求URL（签名作为参数）
String urlWithSign = PAYERMAX_API_URL + "?sign=" + sign;

// 4. 发送到PayerMax（签名在URL参数中，请求体为JSON）
String response = HttpUtil.createPost(urlWithSign)
    .body(requestJson)
    .header("Content-Type", "application/json")
    .execute()
    .body();
```

## 重要注意事项

### 签名参数位置

**关键**: PayerMax的签名`sign`是作为URL参数传递的，不是放在请求体中！

**正确的请求结构**:
```
POST https://pay-gate.payermax.com/aggregate-pay/api/gateway/orderAndPay?sign=<signature>
Content-Type: application/json

{
  "version": "1.4",
  "keyVersion": "1",
  "requestTime": "2025-01-17T09:05:52.194+00:00",
  "appId": "723ce40792ac4dd6b62c92abb433840c",
  "merchantNo": "P01010118567663",
  "data": { ... }
}
```

**错误的方式** (签名在请求体中):
```json
{
  "version": "1.4",
  "sign": "signature_here",  // ❌ 错误！
  "data": { ... }
}
```

### JSON格式一致性

**关键**: 使用商户私钥进行加签的报文字符串要保证和HTTP body中的字符串是一致的，否则会导致PayerMax验签不通过。

**错误示例**:
```json
// 格式化的JSON
{
  "key1": "val1",
  "key2": "val2", 
  "key3": "val3"
}

// 压缩的JSON
{"key1":"val1","key2":"val2","key3":"val3"}
```

这两个JSON内容相同，但格式不同，会导致签名结果不一致！

**正确做法**:
```java
// 1. 先构建请求对象
PayerMaxRequestDTO request = buildPayerMaxRequest(...);

// 2. 转换为JSON字符串（用于签名）
String requestJson = JSONUtil.toJsonStr(request);

// 3. 使用相同的JSON字符串生成签名
String sign = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);

// 4. 构建最终请求（包含签名）
PayerMaxRequestWithSignDTO finalRequest = new PayerMaxRequestWithSignDTO();
// 复制所有字段...
finalRequest.setSign(sign);

// 5. 发送请求（确保JSON格式一致）
String finalJson = JSONUtil.toJsonStr(finalRequest);
```

## 配置参数

在支付配置中需要设置以下参数：

| 参数名 | 说明 | 格式 | 示例 |
|--------|------|------|------|
| privateKey | 商户私钥 | PKCS8格式，Base64编码 | -----BEGIN PRIVATE KEY-----<br/>MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...<br/>-----END PRIVATE KEY----- |
| publicKey | PayerMax公钥 | X509格式，Base64编码 | -----BEGIN PUBLIC KEY-----<br/>MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...<br/>-----END PUBLIC KEY----- |

## 测试验证

### 1. 单元测试

运行 `PayerMaxSignatureTest` 验证签名功能：

```bash
mvn test -Dtest=PayerMaxSignatureTest
```

### 2. 密钥格式验证

```java
@Test
void testKeyValidation() {
    String privateKey = "-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----END PRIVATE KEY-----";
    String publicKey = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----";
    
    boolean isValid = PayerMaxSignatureUtil.validateKeyFormat(privateKey, publicKey);
    assertTrue(isValid);
}
```

### 3. 签名一致性测试

```java
@Test
void testSignatureConsistency() {
    String testData = "{\"key1\":\"val1\",\"key2\":\"val2\"}";
    String privateKey = "your-private-key";
    
    // 多次生成签名应该一致
    String sign1 = PayerMaxSignatureUtil.generateSignature(testData, privateKey);
    String sign2 = PayerMaxSignatureUtil.generateSignature(testData, privateKey);
    
    assertEquals(sign1, sign2);
}
```

## 错误处理

### 常见错误及解决方案

1. **签名验证失败**
   - 检查JSON格式是否一致
   - 确认私钥格式正确（PKCS8）
   - 验证字符编码（UTF-8）

2. **密钥格式错误**
   - 确保私钥是PKCS8格式
   - 确保公钥是X509格式
   - 检查Base64编码是否正确

3. **HTTP请求失败**
   - 检查PayerMax API地址
   - 确认请求头设置正确
   - 验证网络连接

## 生产环境注意事项

1. **密钥安全**
   - 私钥不要硬编码在代码中
   - 使用配置文件或环境变量存储
   - 定期轮换密钥

2. **日志安全**
   - 不要在日志中输出完整的私钥
   - 签名字符串可以记录（用于调试）
   - 敏感数据要脱敏处理

3. **性能优化**
   - 考虑缓存密钥对象
   - 异步处理签名验证
   - 监控签名性能

## 示例代码

完整的PayerMax支付请求示例：

```java
// 1. 构建请求数据
PayerMaxRequestDTO request = new PayerMaxRequestDTO();
request.setVersion("1.4");
request.setKeyVersion("1");
request.setRequestTime(LocalDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
request.setAppId("your-app-id");
request.setMerchantNo("your-merchant-no");

PayerMaxDataDTO data = new PayerMaxDataDTO();
data.setOutTradeNo("ORDER_123456");
data.setSubject("Test Payment");
data.setTotalAmount(10000); // 100.00元，以分为单位
data.setCurrency("TWD");
// ... 设置其他字段

request.setData(data);

// 2. 生成签名（基于请求体JSON）
String requestJson = JSONUtil.toJsonStr(request);
String signature = PayerMaxSignatureUtil.generateSignature(requestJson, privateKey);

// 3. 构建请求URL（签名作为参数）
String apiUrl = "https://pay-gate.payermax.com/aggregate-pay/api/gateway/orderAndPay";
String urlWithSign = apiUrl + "?sign=" + signature;

// 4. 发送请求（签名在URL中，请求体为JSON）
String response = HttpUtil.createPost(urlWithSign)
    .body(requestJson)
    .header("Content-Type", "application/json")
    .execute()
    .body();
```

## 总结

PayerMax签名功能已完全实现，包括：

✅ **完整的签名算法** - RSA SHA256WithRSA  
✅ **格式一致性保证** - 确保JSON字符串一致  
✅ **密钥格式支持** - PKCS8私钥 + X509公钥  
✅ **错误处理机制** - 完善的异常处理  
✅ **测试验证** - 完整的单元测试  
✅ **工具类封装** - 便于使用和维护  

现在可以安全地与PayerMax API进行签名通信了！
