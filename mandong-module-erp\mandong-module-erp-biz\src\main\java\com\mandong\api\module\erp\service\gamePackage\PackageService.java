package com.mandong.api.module.erp.service.gamePackage;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.game.vo.GameVersionRespVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackageAddReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackagePageReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackResultRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskResultDO;

import java.util.List;

public interface PackageService {

    PageResult<GameVersionRespVO> getPage(PackagePageReqVO reqVO);

    Long addPackage(PackageAddReqVO sdkPackGameApkVO);
    Integer deletePackage(Long id);

    List<SdkPackResultRespVO> getResult(Long id);
}
