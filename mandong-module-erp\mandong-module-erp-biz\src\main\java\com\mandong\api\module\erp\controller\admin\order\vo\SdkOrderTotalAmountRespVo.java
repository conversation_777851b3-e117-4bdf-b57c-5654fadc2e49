package com.mandong.api.module.erp.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - Sdk订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkOrderTotalAmountRespVo {

    /**
     * 支付金额
     */
   private float totalAmount;
    /**
     * 未支付金额
     */
   private float totalUnPayAmount;
   
   /**
    * 游戏名称
    */
   private String productName;
}
