package com.mandong.api.module.erp.dal.dataobject.config;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_pays")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkPaysDO {

    private Long id;

    /**
     * 支付方式名称
     */
    @TableField("payName")
    private String payName;

    /**
     * 是否开启：0关闭 1开启
     */
    private int status;

}
