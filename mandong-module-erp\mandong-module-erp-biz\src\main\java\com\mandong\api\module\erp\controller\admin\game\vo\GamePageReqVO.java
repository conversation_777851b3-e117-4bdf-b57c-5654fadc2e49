package com.mandong.api.module.erp.controller.admin.game.vo;

import com.mandong.api.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 游戏分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GamePageReqVO extends PageParam {
    @Schema(description = "游戏名称")
    private List<Long> id;
    @Schema(description = "游戏平台：1 安卓  2ios")
    private Integer platform;


}
