package com.mandong.api.module.erp.controller.app.pay;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付接口测试脚本
 * 
 * 用于测试支付确认接口是否可以正常访问（无需登录）
 *
 * <AUTHOR>
 */
public class PaymentTestScript {
    
    // 测试服务器地址（请根据实际情况修改）
    private static final String BASE_URL = "http://localhost:48080";
    private static final String PAYMENT_API = BASE_URL + "/app-api/pay/confirm-payment";
    
    public static void main(String[] args) {
        System.out.println("=== 支付接口免登录测试 ===");
        
        try {
            // 1. 测试接口可访问性
            testApiAccessibility();
            
            // 2. 测试具体的支付请求
            testPaymentRequest();
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试接口可访问性
     */
    private static void testApiAccessibility() {
        System.out.println("\n1. 测试接口可访问性...");
        
        try {
            // 构建测试请求
            PaymentConfirmReqVO reqVO = new PaymentConfirmReqVO();
            reqVO.setConfigId(1L);
            reqVO.setMethodId(1L);
            reqVO.setAmount(new BigDecimal(1));
            reqVO.setCurrency("TWD");

            reqVO.setUserId("test_user");

            reqVO.setReturnUrl("https://test.com/return");
            
            String requestJson = JSONUtil.toJsonStr(reqVO);
            
            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            
            // 发送请求（不带任何认证信息）
            String response = HttpUtil.createPost(PAYMENT_API)
                    .headerMap(headers, true)
                    .body(requestJson)
                    .execute()
                    .body();
            
            System.out.println("请求URL: " + PAYMENT_API);
            System.out.println("请求体: " + requestJson);
            System.out.println("响应: " + response);
            
            // 检查响应
            if (response.contains("\"code\":401")) {
                System.err.println("❌ 接口仍然需要登录认证！");
            } else if (response.contains("\"code\":403")) {
                System.err.println("❌ 接口权限不足！");
            } else {
                System.out.println("✅ 接口可以正常访问（无需登录）");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 接口访问失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试具体的支付请求
     */
    private static void testPaymentRequest() {
        System.out.println("\n2. 测试支付请求...");
        
        try {
            // 构建完整的支付请求
            PaymentConfirmReqVO reqVO = new PaymentConfirmReqVO();
            reqVO.setConfigId(1L);
            reqVO.setMethodId(1L);
            reqVO.setAmount(new BigDecimal(1)); // 100.00元
            reqVO.setCurrency("TWD");
            reqVO.setUserId("test_user_001");
            reqVO.setReturnUrl("https://example.com/return");
            
            String requestJson = JSONUtil.toJsonStr(reqVO);
            
            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            headers.put("User-Agent", "PaymentTestScript/1.0");
            
            // 发送请求
            String response = HttpUtil.createPost(PAYMENT_API)
                    .headerMap(headers, true)
                    .body(requestJson)
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();
            
            System.out.println("支付请求: " + requestJson);
            System.out.println("支付响应: " + response);
            
            // 分析响应
            analyzeResponse(response);
            
        } catch (Exception e) {
            System.err.println("❌ 支付请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析响应结果
     */
    private static void analyzeResponse(String response) {
        try {
            if (response.contains("\"code\":401")) {
                System.err.println("❌ 认证失败 - 接口仍然需要登录");
            } else if (response.contains("\"code\":403")) {
                System.err.println("❌ 权限不足 - 接口权限配置有问题");
            } else if (response.contains("\"code\":0")) {
                System.out.println("✅ 请求成功 - 接口正常工作");
            } else if (response.contains("\"code\":")) {
                System.out.println("⚠️  业务错误 - 接口可访问但业务逻辑有问题");
                System.out.println("   这是正常的，因为可能缺少配置数据");
            } else {
                System.out.println("❓ 未知响应格式");
            }
        } catch (Exception e) {
            System.err.println("响应分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试不同的HTTP方法
     */
    private static void testDifferentMethods() {
        System.out.println("\n3. 测试不同HTTP方法...");
        
        try {
            // 测试GET请求（应该返回405 Method Not Allowed）
            String getResponse = HttpUtil.get(PAYMENT_API);
            System.out.println("GET响应: " + getResponse);
            
        } catch (Exception e) {
            System.out.println("GET请求失败（这是正常的）: " + e.getMessage());
        }
    }
}
