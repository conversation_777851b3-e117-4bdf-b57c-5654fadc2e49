package com.mandong.api.module.erp.controller.app.pay;

import com.alibaba.fastjson.JSONObject;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigForPaymentVO;
import com.mandong.api.module.erp.controller.app.pay.vo.CallBackPayerMaxRespVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmRespVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import com.mandong.api.module.erp.service.pay.PayConfigService;
import com.mandong.api.module.erp.service.pay.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "支付页面 - 支付配置")
@RestController
@RequestMapping("/pay")
@Validated
public class AppPayConfigController {

    @Resource
    private PaymentService paymentService;

 
    @PostMapping("/confirm-payment")
    @Operation(summary = "确认支付")
    @PermitAll
    public CommonResult<PaymentConfirmRespVO> confirmPayment(@Valid @RequestBody PaymentConfirmReqVO reqVO) {
        PaymentConfirmRespVO response = paymentService.confirmPayment(reqVO);
        return success(response);
    }

    @PostMapping("/callback/payerMax")
    @Operation(summary = "PayerMax支付回调")
    @PermitAll
    public JSONObject payerMaxCallback(@RequestBody CallBackPayerMaxRespVO body) {
        return paymentService.payerMaxCallback(body);
    }

}
