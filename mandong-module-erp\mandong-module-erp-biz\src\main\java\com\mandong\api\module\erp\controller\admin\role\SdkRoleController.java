package com.mandong.api.module.erp.controller.admin.role;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.role.vo.*;
import com.mandong.api.module.erp.service.role.SdkRoleService;
import com.mandong.api.module.erp.service.user.SdkUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - SDK用户")
@RestController
@RequestMapping("/erp/role")
@Validated
public class SdkRoleController {

    @Resource
    private SdkRoleService sdkRoleService;


    @GetMapping("/page")
    @Operation(summary = "获得用户分页")
    @PreAuthorize("@ss.hasPermission('erp:gameRole:query')")
    public CommonResult<PageResult<RolePageRespVO>> getOrderPage(@Valid RolePageReqVO pageReqVO) {

        return success(sdkRoleService.getPage(pageReqVO));
    }



}
