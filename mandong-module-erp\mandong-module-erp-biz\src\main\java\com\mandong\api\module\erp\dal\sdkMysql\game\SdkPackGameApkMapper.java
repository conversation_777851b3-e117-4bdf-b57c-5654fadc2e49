package com.mandong.api.module.erp.dal.sdkMysql.game;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.game.vo.GameVersionRespVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackagePageReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackGameApkVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackGameApkDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("sdkDB")
public interface SdkPackGameApkMapper extends BaseMapperX<SdkPackGameApkDO> {

    default List<GameVersionRespVO> selectGameVersion(Integer id) {
        return selectJoinList(GameVersionRespVO.class,new MPJLambdaWrapperX<SdkPackGameApkDO>()
                .selectAll(SdkPackGameApkDO.class)
                .eqIfPresent(SdkPackGameApkDO::getProductId,id)
                .leftJoin(SdkProductDO.class,SdkProductDO::getId,SdkPackGameApkDO::getProductId)
                .orderByDesc(SdkPackGameApkDO::getUpdateTime)
        );
    }


}