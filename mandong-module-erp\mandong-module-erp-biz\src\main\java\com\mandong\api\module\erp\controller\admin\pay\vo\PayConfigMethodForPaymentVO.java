package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "支付页面 - 支付配置方式 VO")
@Data
public class PayConfigMethodForPaymentVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "配置编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long configId;

    @Schema(description = "支付方式编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long methodId;

    @Schema(description = "支付方式编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "alipay")
    private String methodCode;

    @Schema(description = "支付方式名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝")
    private String methodName;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "图标url", example = "1")
    private String iconUrl;

}
