package com.mandong.api.module.erp.service.pay;

import com.alibaba.fastjson.JSONObject;
import com.mandong.api.module.erp.controller.app.pay.vo.CallBackPayerMaxRespVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmReqVO;
import com.mandong.api.module.erp.controller.app.pay.vo.PaymentConfirmRespVO;
import jakarta.validation.Valid;

/**
 * 支付服务接口
 *
 * <AUTHOR>
 */
public interface PaymentService {

    /**
     * 确认支付
     * 根据支付配置和支付方式，调用相应的第三方支付接口
     *
     * @param reqVO 支付确认请求
     * @return 支付确认响应（包含支付链接）
     */
    PaymentConfirmRespVO confirmPayment(@Valid PaymentConfirmReqVO reqVO);

    JSONObject payerMaxCallback(CallBackPayerMaxRespVO body);
}
