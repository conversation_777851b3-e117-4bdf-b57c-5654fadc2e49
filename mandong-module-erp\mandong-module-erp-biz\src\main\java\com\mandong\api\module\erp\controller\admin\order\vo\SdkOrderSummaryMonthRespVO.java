package com.mandong.api.module.erp.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - Sdk订单交易统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkOrderSummaryMonthRespVO {
    @Schema(description = "日期")
    private String time;
    @Schema(description = "交易金额")
    private Float price;
}
