package com.mandong.api.module.erp.dal.dataobject.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 支付配置与支付方式关联表
 */
@Data
@TableName(value = "pay_config_method")
public class PayConfigMethodDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付配置ID
     */
    @TableField(value = "config_id")
    private Long configId;

    /**
     * 支付方式ID
     */
    @TableField(value = "method_id")
    private Long methodId;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField(value = "`status`")
    private Boolean status;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 支付方式编码（非数据库字段）
     */
    @TableField(exist = false)
    private String methodCode;

    /**
     * 支付方式名称（非数据库字段）
     */
    @TableField(exist = false)
    private String methodName;

    @TableField(exist = false)
    private String iconUrl;
}