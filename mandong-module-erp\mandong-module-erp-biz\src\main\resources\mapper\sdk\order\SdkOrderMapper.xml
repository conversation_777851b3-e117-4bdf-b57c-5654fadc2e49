<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <!--  <select id="page" resultType="com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO">
        select a.orderNo,
               p.productName,
               qc.channelName,
               a.username,
               a.roleName,
               a.roleId,
               a.server<PERSON>ame,
               a.roleLevel,
               a.orderGoodsId,
               a.dealAmount,
               qp.payName,
               a.payTime,
               a.payStatus
        from qsdk_order a
                 left join qsdk_product p on a.productId = p.id
                 left join quick_sdk_platform.qsdk_channel qc on a.productId = qc.productId and a.channelCode = qc.channelCode
                 left join qsdk_pays qp on a.payType = qp.id
        where a.payStatus = 1
          and a.asyncStatus = 1
        order by a.createTime desc
    </select>-->

    <select id="selectSeverByProductId" resultType="java.lang.String" >
        select serverName from qsdk_order
        <where>
            AND  productId IN
            <foreach collection="productId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        group by serverName
        order by CAST(REPLACE(SUBSTRING_INDEX(serverName, '-', -1), '구', '') AS UNSIGNED) desc
    </select>



    <select id="selectPageByServer" resultType="com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO">
        select  t.serverName, sum(t.dealAmount) amount
        from qsdk_order t
        left join qsdk_product p on t.productId = p.id

        <where>
            <if test="reqVO.productId != null and reqVO.productId.size() != 0">
                and t.productId  in
                <foreach collection="reqVO.productId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="reqVO.serverName != null and reqVO.serverName != ''">
                and t.serverName = #{reqVO.serverName}
            </if>
            <if test="reqVO.payTime != null and reqVO.payTime.length != 0">
                and t.payTime between #{reqVO.payTime[0]} and #{reqVO.payTime[1]}
            </if>
        </where>
        group by   t.serverName
        order by CAST(REPLACE(SUBSTRING_INDEX(t.serverName, '-', -1), '구', '') AS UNSIGNED) desc

    </select>

    <select id="selectChannelDetails"
            resultType="com.mandong.api.module.erp.controller.admin.server.vo.ServerChannelDetailsRespVO">

        select t.productId,p.productName,t.channelCode,c.channelName  , sum(t.dealAmount) as amount
        from qsdk_order t
            left join qsdk_channel c on (t.channelCode = c.channelCode and t.productId = c.productId)
            left join qsdk_product p on t.productId = p.id
        <where>
            <if test="reqVO.productId != null and reqVO.productId.size() != 0">
                and t.productId  in
                <foreach collection="reqVO.productId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="reqVO.serverName != null and reqVO.serverName != ''">
                and t.serverName = #{reqVO.serverName}
            </if>
            <if test="reqVO.payTime != null and reqVO.payTime.length != 0">
                and t.payTime between #{reqVO.payTime[0]} and #{reqVO.payTime[1]}
            </if>
        </where>

        group by t.productId, t.channelCode


    </select>
    <select id="getOperationDailyPayUsers"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        COUNT(DISTINCT username) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.channelCode = #{condition.channelCode})
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="channelCode != null and channelCode.size() > 0">
                    AND o.channelCode IN
                    <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>

    <select id="getOperationDailyPay"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        COUNT(dealAmount) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.channelCode = #{condition.channelCode})
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="channelCode != null and channelCode.size() > 0">
                    AND o.channelCode IN
                    <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>
    <select id="getOperationDailyTradeAmount"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        SUM(dealAmount) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.channelCode = #{condition.channelCode})
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="channelCode != null and channelCode.size() > 0">
                    AND o.channelCode IN
                    <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>
    <select id="countOperationWeekPayUsers" resultType="java.lang.Long">
        SELECT COUNT(1) as weekPayUser
        FROM (
        SELECT o.username
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.channelCode = #{condition.channelCode})
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="channelCode != null and channelCode.size() > 0">
                    AND o.channelCode IN
                    <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY o.username
        ) t
    </select>
    <select id="getOperationUserPaySummaryByDay"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO">
        SELECT COUNT(1) as todayPayUser
        FROM (
        SELECT o.username
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        <if test="payTime != null">
            AND o.payTime BETWEEN #{payTime[0]} AND #{payTime[1]}
        </if>
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.channelCode = #{condition.channelCode})
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="channelCode != null and channelCode.size() > 0">
                    AND o.channelCode IN
                    <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY o.username
        ) t
    </select>
    <select id="getDailyPayUsers"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        COUNT(DISTINCT username) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                    <if test="condition.channelCode != null and condition.channelCode != ''">
                        and o.channelCode = #{condition.channelCode}
                    </if>
                    )
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="serverNames != null and serverNames.size() > 0">
                    AND o.serverName IN
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>
    <select id="getDailyPay"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        COUNT(dealAmount) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                    <if test="condition.channelCode != null and condition.channelCode != ''">
                        and o.channelCode = #{condition.channelCode}
                    </if>
                    )
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="serverNames != null and serverNames.size() > 0">
                    AND o.serverName IN
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>
    <select id="getDailyTradeAmount"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO">
        SELECT
        days.day as time,
        IFNULL(t.price, 0) as price
        FROM (
        SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
        SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
        SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
        SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
        SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
        SELECT 31
        ) days
        LEFT JOIN (
        SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
        FROM_UNIXTIME(payTime, '%d') as day,
        SUM(dealAmount) as price
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                    <if test="condition.channelCode != null and condition.channelCode != ''">
                        and o.channelCode = #{condition.channelCode}
                    </if>
                    )
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="serverNames != null and serverNames.size() > 0">
                    AND o.serverName IN
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY full_date, day
        ) t ON days.day = t.day
        WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
        AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
        ORDER BY days.day ASC
    </select>
    <select id="countWeekPayUsers" resultType="java.lang.Long">
        SELECT COUNT(1) as weekPayUser
        FROM (
        SELECT o.username
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        AND o.payTime BETWEEN #{startTime} AND #{endTime}
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                    <if test="condition.channelCode != null and condition.channelCode != ''">
                        and o.channelCode = #{condition.channelCode}
                    </if>
                    )
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="serverNames != null and serverNames.size() > 0">
                    AND o.serverName IN
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY o.username
        ) t
    </select>
    <select id="getOrderUserPaySummaryByDay"
            resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO">
        SELECT COUNT(1) as todayPayUser
        FROM (
        SELECT o.username
        FROM qsdk_order o
        LEFT JOIN qsdk_product p ON p.id = o.productId
        WHERE o.payStatus = 1
        <if test="payTime != null">
            AND o.payTime BETWEEN #{payTime[0]} AND #{payTime[1]}
        </if>
        <choose>
            <when test="conditions != null and conditions.size() > 0">
                AND (
                <foreach collection="conditions" item="condition" separator=" OR ">
                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                    <if test="condition.channelCode != null and condition.channelCode != ''">
                        and o.channelCode = #{condition.channelCode}
                    </if>

                    )
                </foreach>
                )
            </when>
            <otherwise>
                <if test="productId != null and productId.size() > 0">
                    AND p.id IN
                    <foreach collection="productId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="serverNames != null and serverNames.size() > 0">
                    AND o.serverName IN
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        GROUP BY o.username
        ) t
    </select>

    <select id="selectPageByLeaderGroup" resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo">
        SELECT DISTINCT
        t.orderNo,
        t.id,
        t.uid,
        t.channelCode,
        t.username,
        t.roleName,
        t.roleId,
        t.roleLevel,
        t.serverName,
        t.productId,
        t.payStatus,
        t.payTime,
        t.payType,
        t.asyncStatus,
        t.createTime,
        IF(t.payStatus = 1, t.dealAmount, t.amount) AS dealAmount,
        channel.channelName,
        t2.productName,
        t3.payName,
        IF(o.user_name IS NULL, channel.channelName, o.user_name) AS optName
        FROM qsdk_order t FORCE INDEX(qsdk_order_createTime_id_index)
        LEFT JOIN qsdk_channel channel
        ON channel.productId = t.productId
        AND channel.channelCode = t.channelCode
        LEFT JOIN qsdk_product t2 ON t2.id = t.productId
        LEFT JOIN qsdk_pays t3 ON t3.id = t.payType
        LEFT JOIN qsdk_opt_link t4
        ON t.productId = t4.product_id
        AND t.channelCode = t4.channel_code
        LEFT JOIN qsdk_opt o ON o.id = t4.link_id
        WHERE 1=1
        <if test="reqVO.createTime != null and reqVO.createTime.length != 0">
            AND t.createTime BETWEEN #{reqVO.createTime[0]} and #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.orderNo !=null and reqVO.orderNo != ''">
            AND t.orderNo = #{reqVO.orderNo}
        </if>
        <if test="reqVO.productId != null and reqVO.productId.size() > 0">
            AND t.productId in
            <foreach collection="reqVO.productId" open="(" close=")" separator="," item="productId">
               #{productId}
            </foreach>
        </if>
        AND EXISTS (
        SELECT 1
        FROM qsdk_lead_group t1
        INNER JOIN qsdk_lead_group_link bc ON t1.id = bc.link_id
        LEFT JOIN qsdk_opt t2 ON t2.group_name = bc.opt_group_name
        LEFT JOIN qsdk_opt_link t3 ON t3.link_id = t2.id
        AND t3.product_id = bc.product_id
        WHERE t1.uid = #{userId}
        AND (bc.opt_group_name IS NULL OR t3.channel_code IS NOT NULL)
        AND t.productId = bc.product_id
        AND (bc.server_name IS NULL OR t.serverName = bc.server_name)
        AND (t3.channel_code IS NULL OR t.channelCode = t3.channel_code)
        )
        <if test="reqVO.channelCode != null and reqVO.channelCode.size() != 0 ">
            AND t.channelCode in
            <foreach collection="reqVO.channelCode" separator="," close=")" open="(" item="channelCode">
                #{channelCode}
            </foreach>
        </if>
        <if test="reqVO.payment != null and reqVO.payment != ''">
            AND t.payType = #{reqVO.payment}
        </if>
        <if test="reqVO.payStatus != null and reqVO.payStatus != ''">
            AND t.payStatus = #{reqVO.payStatus}
        </if>
        <if test="reqVO.username != null and reqVO.username != ''">
            AND t.username = #{reqVO.username}
        </if>
        <if test="reqVO.roleId != null and reqVO.roleId != ''">
            AND t.roleId = #{reqVO.roleId}
        </if>
        <if test="reqVO.roleName != null and reqVO.roleName != ''">
            AND t.roleName = #{reqVO.roleName}
        </if>
        <if test="reqVO.serverName != null and reqVO.serverName != ''">
            AND t.serverName = #{reqVO.serverName}
        </if>
        <if test="reqVO.uid != null and reqVO.uid != ''">
            AND t.uid = #{reqVO.uid}
        </if>
        <if test="reqVO.payTime != null and reqVO.payTime.length() != 0 ">
            AND t.payTime BETWEEN #{reqVO.payTime[0]} and #{reqVO.payTime[1]}
        </if>
        <if test="reqVO.payName != null and reqVO.payName != ''">
            AND t3.payName = #{reqVO.payName}
        </if>
        ORDER BY t.id DESC
    </select>

    <select id="selectPageByLeaderGroupTotalPage" resultType="com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderTotalAmountRespVo">
        SELECT  SUM(t.dealAmount) AS totalAmount
        FROM qsdk_order t FORCE INDEX(qsdk_order_createTime_id_index)
        LEFT JOIN qsdk_channel channel
        ON channel.productId = t.productId
        AND channel.channelCode = t.channelCode
        LEFT JOIN qsdk_product t2 ON t2.id = t.productId
        LEFT JOIN qsdk_pays t3 ON t3.id = t.payType
        LEFT JOIN qsdk_opt_link t4
        ON t.productId = t4.product_id
        AND t.channelCode = t4.channel_code
        LEFT JOIN qsdk_opt o ON o.id = t4.link_id
        WHERE 1=1
        <if test="reqVO.createTime != null and reqVO.createTime.length != 0">
            AND t.createTime BETWEEN #{reqVO.createTime[0]} and #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.orderNo !=null and reqVO.orderNo != ''">
            AND t.orderNo = #{reqVO.orderNo}
        </if>
        <if test="reqVO.productId != null and reqVO.productId.size() > 0">
            AND t.productId in
            <foreach collection="reqVO.productId" open="(" close=")" separator="," item="productId">
                #{productId}
            </foreach>
        </if>
        AND EXISTS (
        SELECT 1
        FROM qsdk_lead_group t1
        INNER JOIN qsdk_lead_group_link bc ON t1.id = bc.link_id
        LEFT JOIN qsdk_opt t2 ON t2.group_name = bc.opt_group_name
        LEFT JOIN qsdk_opt_link t3 ON t3.link_id = t2.id
        AND t3.product_id = bc.product_id
        WHERE t1.uid = #{userId}
        AND (bc.opt_group_name IS NULL OR t3.channel_code IS NOT NULL)
        AND t.productId = bc.product_id
        AND (bc.server_name IS NULL OR t.serverName = bc.server_name)
        AND (t3.channel_code IS NULL OR t.channelCode = t3.channel_code)
        )
        <if test="reqVO.channelCode != null and reqVO.channelCode.size() != 0 ">
            AND t.channelCode in
            <foreach collection="reqVO.channelCode" separator="," close=")" open="(" item="channelCode">
                #{channelCode}
            </foreach>
        </if>
        <if test="reqVO.payment != null and reqVO.payment != ''">
            AND t.payType = #{reqVO.payment}
        </if>
        <if test="reqVO.payStatus != null and reqVO.payStatus != ''">
            AND t.payStatus = #{reqVO.payStatus}
        </if>
        <if test="reqVO.username != null and reqVO.username != ''">
            AND t.username = #{reqVO.username}
        </if>
        <if test="reqVO.roleId != null and reqVO.roleId != ''">
            AND t.roleId = #{reqVO.roleId}
        </if>
        <if test="reqVO.roleName != null and reqVO.roleName != ''">
            AND t.roleName = #{reqVO.roleName}
        </if>
        <if test="reqVO.serverName != null and reqVO.serverName != ''">
            AND t.serverName = #{reqVO.serverName}
        </if>
        <if test="reqVO.uid != null and reqVO.uid != ''">
            AND t.uid = #{reqVO.uid}
        </if>
        <if test="reqVO.payTime != null and reqVO.payTime.length() != 0 ">
            AND t.payTime BETWEEN #{reqVO.payTime[0]} and #{reqVO.payTime[1]}
        </if>
        <if test="reqVO.payName != null and reqVO.payName != ''">
            AND t3.payName = #{reqVO.payName}
        </if>

    </select>


</mapper>