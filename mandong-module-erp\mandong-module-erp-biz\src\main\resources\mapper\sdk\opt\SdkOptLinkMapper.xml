<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptLinkMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="getOptMonthlyStats"
            resultType="com.mandong.api.module.erp.controller.admin.opt.vo.OptMonthlyStatsVO">
        select FROM_UNIXTIME(o.payTime, '%Y-%m') month, p.productName productName, l.channel_name channelName, sum(o.dealAmount) amount
        from qsdk_opt t
                 left join qsdk_opt_link l on t.id = l.link_id
                 left join qsdk_order o on o.productId = l.product_id and o.channelCode = l.channel_code
                 left join qsdk_product p on o.productId = p.id
        where o.payStatus = 1
          and t.id = #{id}
            and o.payTime between #{startTime} and #{endTime}
        group by FROM_UNIXTIME(o.payTime, '%Y-%m'), p.productName,l.channel_name

    </select>
</mapper>