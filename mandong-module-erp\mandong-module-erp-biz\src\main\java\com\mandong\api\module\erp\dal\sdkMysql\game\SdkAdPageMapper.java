package com.mandong.api.module.erp.dal.sdkMysql.game;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("sdkDB")
public interface SdkAdPageMapper extends BaseMapperX<SdkAdPageDO> {

    default List<SdkAdPageDO> getAdPage(Integer id) {
        return selectList(SdkAdPageDO::getProductId,id);
    }
}