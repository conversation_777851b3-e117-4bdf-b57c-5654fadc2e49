package com.mandong.api.module.erp.dal.dataobject.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_pays")
public class SdkPays {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "payName")
    private String payname;

    @TableField(value = "payDesc")
    private String paydesc;

    @TableField(value = "payLogo")
    private String paylogo;

    @TableField(value = "payModel")
    private String paymodel;

    /**
     * 0关闭 1开启
     */
    @TableField(value = "`status`")
    private Boolean status;

    @TableField(value = "lang")
    private String lang;

    /**
     * 是否支持wap 1支持 0不支持
     */
    @TableField(value = "supportWap")
    private Boolean supportwap;

    /**
     * 主支付渠道id,默认0为主渠道
     */
    @TableField(value = "parentId")
    private Integer parentid;

    /**
     * 是否固定支付金额(0不固定,1固定)
     */
    @TableField(value = "isFixed")
    private Boolean isfixed;

    /**
     * 子渠道支付方式编号
     */
    @TableField(value = "typeCode")
    private String typecode;
}