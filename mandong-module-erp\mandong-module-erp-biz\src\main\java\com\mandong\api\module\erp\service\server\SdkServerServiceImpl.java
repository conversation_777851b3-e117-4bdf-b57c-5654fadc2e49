package com.mandong.api.module.erp.service.server;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerChannelDetailsRespVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageReqVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserSummaryRespVO;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class SdkServerServiceImpl implements SdkServerService {

    @Resource
    private SdkOrderMapper sdkOrderMapper;
    @Resource
    private SdkUserMapper sdkUserMapper;


    @Override
    public PageResult<ServerPageRespVO> getPage(ServerPageReqVO pageReqVO) {
        Page<ServerPageRespVO> serverPageRespVOPage = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        sdkOrderMapper.selectPageByServer(serverPageRespVOPage,pageReqVO);

        return new PageResult<>(serverPageRespVOPage.getRecords(), serverPageRespVOPage.getTotal()) ;
    }

    @Override
    public List<ServerChannelDetailsRespVO> getChannelDetails(ServerPageReqVO pageReqVO) {
        List<ServerChannelDetailsRespVO> serverChannelDetailsRespVOS = sdkOrderMapper.selectChannelDetails(pageReqVO);
        for (ServerChannelDetailsRespVO serverChannelDetailsRespVO : serverChannelDetailsRespVOS) {
            SdkOrderSummaryReqVO reqVO = new SdkOrderSummaryReqVO();
            reqVO.setServerName(pageReqVO.getServerName());
            ArrayList<String> channelCodes = new ArrayList<>();
            channelCodes.add(serverChannelDetailsRespVO.getChannelCode());
            reqVO.setChannelCode(channelCodes);
            reqVO.setProductId(pageReqVO.getProductId());
            reqVO.setPayTime(pageReqVO.getPayTime());

            SdkUserSummaryRespVO userRegistrationByDay = sdkUserMapper.getUserRegistrationByDay(reqVO);
            serverChannelDetailsRespVO.setRegisterCount(userRegistrationByDay.getTodayRegistration());
        }


        return serverChannelDetailsRespVOS;
    }
}
