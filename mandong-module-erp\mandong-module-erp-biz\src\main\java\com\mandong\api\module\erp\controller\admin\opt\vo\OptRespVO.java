package com.mandong.api.module.erp.controller.admin.opt.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 带队归属 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OptRespVO {
    private Long id;
    private Long uid;
    @Schema(description = "组名")
    private String groupName;

    @Schema(description = "带队账号")
    private String userName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
