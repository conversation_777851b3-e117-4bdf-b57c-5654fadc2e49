package com.mandong.api.module.erp.dal.sdkMysql.game;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.game.vo.GameTaskListReqVO;
import com.mandong.api.module.erp.controller.admin.game.vo.GameVersionRespVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackagePageReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskResultDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper
@DS("sdkDB")
public interface SdkPackTaskMapper extends BaseMapperX<SdkPackTaskDO> {

    default PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO, List<SdkOperationCondition> operationPermittedConditions){
        MPJLambdaWrapper<SdkPackTaskDO> wrapper = new MPJLambdaWrapperX<SdkPackTaskDO>()
                .selectAll(SdkPackTaskDO.class)
                .eqIfPresent(SdkPackTaskDO::getProductId, reqVO.getProductId())
                .eq(SdkPackTaskDO::getStatus, 1)
                .select(SdkPackTaskResultDO::getChannelCode, SdkPackTaskResultDO::getApkUrl)
                .leftJoin(SdkPackTaskResultDO.class, SdkPackTaskResultDO::getTaskId, SdkPackTaskDO::getId)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, on ->
                        on.eq(SdkChannelDO::getProductId, SdkPackTaskDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkPackTaskResultDO::getChannelCode))
                .orderByDesc(SdkPackTaskDO::getCreateTime);
        if (operationPermittedConditions != null && !operationPermittedConditions.isEmpty()) {
            List<String> channelCodes = new ArrayList<>();
            for (int i = 0; i < operationPermittedConditions.size(); i++) {
                com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition condition = operationPermittedConditions.get(i);
                if (condition.getProductId().equals(reqVO.getProductId())) {
                    channelCodes.add(condition.getChannelCode());
                }

            }
            if (!channelCodes.isEmpty()) {
                wrapper.in(SdkChannelDO::getChannelCode, channelCodes);
            }
        }



        return selectJoinPage(reqVO, GameVersionRespVO.class, wrapper);
    }

    default PageResult<GameVersionRespVO> getPackagePage(PackagePageReqVO reqVO ){
        MPJLambdaWrapper<SdkPackTaskDO> wrapper = new MPJLambdaWrapperX<SdkPackTaskDO>()
                .selectAll(SdkPackTaskDO.class)
                .inIfPresent(SdkPackTaskDO::getProductId, reqVO.getProductId())
                .eqIfPresent(SdkPackTaskDO::getStatus, reqVO.getStatus())

                .likeIfExists(SdkChannelDO::getChannelName,reqVO.getChannelName())
                .eq(SdkPackTaskDO::getStatus,1)

                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class,SdkProductDO::getId,SdkPackTaskDO::getProductId)
                .orderByDesc(SdkPackTaskDO::getCreateTime);




        return selectJoinPage(reqVO, GameVersionRespVO.class, wrapper);
    }

}