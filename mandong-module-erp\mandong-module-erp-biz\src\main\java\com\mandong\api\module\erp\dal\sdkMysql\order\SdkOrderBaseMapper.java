package com.mandong.api.module.erp.dal.sdkMysql.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * SDK订单基础查询 Mapper
 * 包含基础的分页查询和总金额查询方法
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkOrderBaseMapper extends BaseMapperX<SdkOrderDO> {

    /**
     * 基础分页查询
     */
    default PageResult<SdkOrderRespVo> selectPage(OrderPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, SdkOrderRespVo.class, new MPJLambdaWrapperX<SdkOrderDO>()
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .eqIfPresent(SdkOrderDO::getUid, pageReqVO.getUid())
                .inIfExists(SdkChannelDO::getChannelCode, pageReqVO.getChannelCode())
                .inIfExists(SdkProductDO::getId, pageReqVO.getProductId())
                .inIfExists(SdkOrderDO::getServerName, pageReqVO.getServers())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName())
                .select(SdkOrderDO::getOrderNo, SdkOrderDO::getId, SdkOrderDO::getUid, SdkOrderDO::getChannelCode, 
                       SdkOrderDO::getUsername, SdkOrderDO::getRoleName, SdkOrderDO::getRoleId, SdkOrderDO::getRoleLevel, 
                       SdkOrderDO::getServerName, SdkOrderDO::getProductId, SdkOrderDO::getPayStatus, SdkOrderDO::getPayTime, 
                       SdkOrderDO::getPayType, SdkOrderDO::getAsyncStatus, SdkOrderDO::getCreateTime)
                .select("IF(t.payStatus=1,t.dealAmount,t.amount) AS dealAmount")
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on -> 
                    on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                      .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o -> 
                    o.eq(SdkOrderDO::getProductId, SdkOptLinkDO::getProductId)
                     .eq(SdkOrderDO::getChannelCode, SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class, "o", SdkOptDO::getId, SdkOptLinkDO::getLinkId)
                .orderByDesc(SdkOrderDO::getId)
        );
    }

    /**
     * 基础总金额查询
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmount(OrderPageReqVO pageReqVO) {
        return selectJoinOne(SdkOrderTotalAmountRespVo.class, new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderTotalAmountRespVo::getTotalAmount)
                .eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkOrderDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .inIfExists(SdkChannelDO::getChannelCode, pageReqVO.getChannelCode())
                .inIfExists(SdkProductDO::getId, pageReqVO.getProductId())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName())
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on -> 
                    on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                      .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
        );
    }

    /**
     * 根据用户ID查询订单列表
     */
    default List<SdkOrderRespVo> selectPageByUid(Long id) {
        return selectJoinList(SdkOrderRespVo.class, new MPJLambdaWrapperX<SdkOrderDO>()
                .selectAll(SdkOrderDO.class)
                .eqIfPresent(SdkOrderDO::getUid, id)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on -> 
                    on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                      .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .orderByDesc(SdkOrderDO::getId)
        );
    }

    /**
     * 使用产品ID和区服名的组合条件进行分页查询
     */
    default PageResult<SdkOrderRespVo> selectPageWithConditions(OrderPageReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = (MPJLambdaWrapperX<SdkOrderDO>) new MPJLambdaWrapperX<SdkOrderDO>()
                .select(SdkOrderDO::getOrderNo, SdkOrderDO::getId, SdkOrderDO::getUid, SdkOrderDO::getChannelCode, 
                       SdkOrderDO::getUsername, SdkOrderDO::getRoleName, SdkOrderDO::getRoleId, SdkOrderDO::getRoleLevel, 
                       SdkOrderDO::getServerName, SdkOrderDO::getProductId, SdkOrderDO::getPayStatus, SdkOrderDO::getPayTime, 
                       SdkOrderDO::getPayType, SdkOrderDO::getAsyncStatus, SdkOrderDO::getCreateTime)
                .select("IF(t.payStatus=1,t.dealAmount,t.amount) AS dealAmount");

        // 添加通用查询条件
        queryWrapper.eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .inIfExists(SdkChannelDO::getChannelCode, pageReqVO.getChannelCode())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName());

        // 处理特定的产品和区服组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        if (condition.getChannelCode() == null) {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName())
                                             .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    } else {
                        if (condition.getChannelCode() == null) {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName())
                                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                }
            });
        }

        // 添加连接查询
        queryWrapper.select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on ->
                        on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o -> 
                    o.eq(SdkOrderDO::getProductId, SdkOptLinkDO::getProductId)
                     .eq(SdkOrderDO::getChannelCode, SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class, "o", SdkOptDO::getId, SdkOptLinkDO::getLinkId)
                .orderByDesc(SdkOrderDO::getId);

        return selectJoinPage(pageReqVO, SdkOrderRespVo.class, queryWrapper);
    }

    /**
     * 使用产品ID和区服名的组合条件计算总金额
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmountWithConditions(OrderPageReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = new MPJLambdaWrapperX<SdkOrderDO>()
                .selectSum(SdkOrderDO::getDealAmount, SdkOrderTotalAmountRespVo::getTotalAmount);

        // 添加通用查询条件
        queryWrapper.eqIfPresent(SdkOrderDO::getOrderNo, pageReqVO.getOrderNo())
                .likeIfPresent(SdkOrderDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkOrderDO::getRoleId, pageReqVO.getRoleId())
                .likeIfPresent(SdkOrderDO::getRoleName, pageReqVO.getRoleName())
                .betweenIfPresent(SdkOrderDO::getCreateTime, pageReqVO.getCreateTime())
                .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime())
                .eqIfPresent(SdkOrderDO::getPayStatus, pageReqVO.getPayStatus())
                .inIfExists(SdkChannelDO::getChannelCode, pageReqVO.getChannelCode())
                .eqIfExists(SdkPaysDO::getPayName, pageReqVO.getPayName());

        // 处理特定的产品和区服组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        if (condition.getChannelCode() == null) {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName())
                                             .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    } else {
                        if (condition.getChannelCode() == null) {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        } else {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName())
                                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        }
                    }
                }
            });
        }

        // 添加连接查询
        queryWrapper.select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class, "channel", on ->
                        on.eq(SdkChannelDO::getProductId, SdkOrderDO::getProductId)
                                .eq(SdkChannelDO::getChannelCode, SdkOrderDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                .select(SdkPaysDO::getPayName)
                .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType);

        return selectJoinOne(SdkOrderTotalAmountRespVo.class, queryWrapper);
    }

    /**
     * 使用VO对象条件查询订单列表
     */
    default PageResult<SdkOrderRespVo> selectPageWithVoConditions(OrderPageReqVO pageReqVO,
                                                                  List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            PageResult<SdkOrderRespVo> emptyResult = new PageResult<>();
            emptyResult.setList(new ArrayList<>());
            emptyResult.setTotal(0L);
            return emptyResult;
        }
        return selectPageWithConditions(pageReqVO, conditions);
    }

    /**
     * 使用VO对象条件查询订单总金额
     */
    default SdkOrderTotalAmountRespVo selectPageTotalAmountWithVoConditions(OrderPageReqVO pageReqVO,
                                                                            List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            SdkOrderTotalAmountRespVo emptyResult = new SdkOrderTotalAmountRespVo();
            emptyResult.setTotalAmount(0F);
            return emptyResult;
        }
        return selectPageTotalAmountWithConditions(pageReqVO, conditions);
    }
}
