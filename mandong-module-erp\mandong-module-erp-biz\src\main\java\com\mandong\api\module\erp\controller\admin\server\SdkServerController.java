package com.mandong.api.module.erp.controller.admin.server;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerChannelDetailsRespVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageReqVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserPageReqVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserPageRespVO;
import com.mandong.api.module.erp.service.server.SdkServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 区服")
@RestController
@RequestMapping("/erp/server")
@Validated
public class SdkServerController {

    @Resource
    private SdkServerService sdkServerService;

    @GetMapping("/page")
    @Operation(summary = "获得用户分页")
    @PreAuthorize("@ss.hasPermission('erp:server:query')")
    public CommonResult<PageResult<ServerPageRespVO>> getPage(@Valid ServerPageReqVO pageReqVO) {

        return success(sdkServerService.getPage(pageReqVO));
    }

    @GetMapping("/channelDetails")
    @Operation(summary = "获得区服渠道数据")
    @PreAuthorize("@ss.hasPermission('erp:server:query')")
    public CommonResult<List<ServerChannelDetailsRespVO>> getChannelDetails(@Valid ServerPageReqVO pageReqVO) {

        return success(sdkServerService.getChannelDetails(pageReqVO));
    }

}
