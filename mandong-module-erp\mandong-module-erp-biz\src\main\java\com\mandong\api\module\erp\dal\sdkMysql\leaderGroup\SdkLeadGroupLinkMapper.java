package com.mandong.api.module.erp.dal.sdkMysql.leaderGroup;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupDO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.hutool.core.date.DateTime;

import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkLeadGroupLinkMapper extends BaseMapperX<SdkLeadGroupLinkDO> {

    default PageResult<LeadGroupLinksPageRespVO> selectPage(LeadGroupLinksPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, LeadGroupLinksPageRespVO.class, new MPJLambdaWrapperX<SdkLeadGroupLinkDO>()
                .selectAll(SdkLeadGroupLinkDO.class)
                .eqIfPresent(SdkLeadGroupLinkDO::getLinkId, pageReqVO.getLinkId())
                .inIfPresent(SdkLeadGroupLinkDO::getProductId, pageReqVO.getProductId())
                .eqIfPresent(SdkLeadGroupLinkDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkLeadGroupLinkDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(SdkLeadGroupLinkDO::getCreateTime)
        );
    }

      List<LeadGroupOptVO> selectListByOpt(@Param("id") Long id);


    List<LeadGroupOptVO> selectListByOptUserId(@Param("userId") Long userId);


    List<LeadGroupMonthlyStatsVO> getLeadGroupMonthlyStats(@Param("id") Long id,@Param("startTime") Long startTime,@Param("endTime")Long endTime,@Param("condition") List<LeadGroupOptVO> condition) ;
}