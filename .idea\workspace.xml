<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BuildServerSettings">
    <option name="LOGIN" value="admin" />
    <option name="SERVER_URL" value="http://*************:8111" />
    <treeState key="project">
      <selectedPath>
        <pathElement>
          <option name="myNodeId" value="helperText" />
          <option name="myNodeType" value="jetbrains.buildServer.tree.descriptors.LinkedTextDescriptor" />
        </pathElement>
      </selectedPath>
    </treeState>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d0ee2f0b-cf61-4948-a09a-bd94043f9ead" name="更改" comment="支付">
      <change beforePath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/controller/admin/game/GameController.java" beforeDir="false" afterPath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/controller/admin/game/GameController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameService.java" beforeDir="false" afterPath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mandong-server/src/main/resources/application-local.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/mandong-server/src/main/resources/application-local.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mandong-server/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/mandong-server/src/main/resources/application.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/dynamic-datasource-spring/4.3.1/dynamic-datasource-spring-4.3.1.jar!/com/baomidou/dynamic/datasource/annotation/DS.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar!/com/baomidou/mybatisplus/core/mapper/BaseMapper.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar!/com/baomidou/mybatisplus/core/override/MybatisMapperProxy.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/github/yulichang/mybatis-plus-join-core/1.4.13/mybatis-plus-join-core-1.4.13-sources.jar!/com/github/yulichang/wrapper/interfaces/CompareIfExists.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/github/yulichang/mybatis-plus-join-core/1.4.13/mybatis-plus-join-core-1.4.13-sources.jar!/com/github/yulichang/wrapper/interfaces/Func.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/github/yulichang/mybatis-plus-join-core/1.4.13/mybatis-plus-join-core-1.4.13.jar!/com/github/yulichang/wrapper/interfaces/Func.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-web/6.2.1/spring-web-6.2.1.jar!/org/springframework/web/bind/annotation/RequestBody.class" root0="SKIP_INSPECTION" />
    <setting file="jar://D:/jdk-17.0.1/lib/src.zip!/java.base/java/util/List.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
    <option name="hasSeenReactiveStreamsDisablingDialog" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2uAIvuhMFIhzzfEW0B9YQxLJwMt" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Docker.mandong-server/Dockerfile.executor": "Run",
    "HTTP 请求.generated-requests | #1.executor": "Run",
    "HTTP 请求.generated-requests | #2.executor": "Run",
    "Maven.mandong [clean].executor": "Run",
    "Maven.mandong [install].executor": "Run",
    "Maven.mandong [package].executor": "Run",
    "Maven.mandong [test].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.MandongServerApplication.executor": "Debug",
    "extract.method.default.visibility": "public",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/mandong/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/opt",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "http.proxy",
    "vue.rearranger.settings.migration": "true",
    "应用程序.PackageServiceImpl.executor": "Run",
    "应用程序.PaymentServiceImpl.executor": "Run"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
    <option name="stackFrameCustomizationEnabled" value="false" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\service\opt" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\controller\admin\opt" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\opt" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\leadGroup" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\role" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\pay" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\java\com\mandong\api\module\erp\dal\dataobject\leadGroup" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\channel" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk" />
      <recent name="C:\Users\<USER>\IdeaProjects\mandong\mandong-module-erp\mandong-module-erp-biz\src\main\resources\mapper\sdk\order" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.mandong.api.module.erp.job" />
      <recent name="com.mandong.api.module.system.job" />
      <recent name="com.mandong.api.module.erp.controller.admin.site.vo" />
      <recent name="com.mandong.api.module.erp.controller.admin.site" />
      <recent name="com.mandong.api.module.erp.service.site.vo" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MandongServerApplication">
    <configuration name="PackageServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.mandong.api.module.erp.service.gamePackage.PackageServiceImpl" />
      <module name="mandong-module-erp-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.mandong.api.module.erp.service.gamePackage.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PaymentServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.mandong.api.module.erp.service.pay.PaymentServiceImpl" />
      <module name="mandong-module-erp-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.mandong.api.module.erp.service.pay.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="generated-requests | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#1" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #2" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#2" index="2" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="MandongServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="mandong-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.mandong.api.server.MandongServerApplication" />
      <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="mandong-server/Dockerfile" type="docker-deploy" factoryName="dockerfile" temporary="true" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="mandong-server/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.mandong-server/Dockerfile" />
      <item itemvalue="HTTP 请求.generated-requests | #1" />
      <item itemvalue="HTTP 请求.generated-requests | #2" />
      <item itemvalue="Spring Boot.MandongServerApplication" />
      <item itemvalue="应用程序.PaymentServiceImpl" />
      <item itemvalue="应用程序.PackageServiceImpl" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.PaymentServiceImpl" />
        <item itemvalue="应用程序.PackageServiceImpl" />
        <item itemvalue="Docker.mandong-server/Dockerfile" />
        <item itemvalue="HTTP 请求.generated-requests | #2" />
        <item itemvalue="HTTP 请求.generated-requests | #1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d0ee2f0b-cf61-4948-a09a-bd94043f9ead" name="更改" comment="" />
      <created>1741685186158</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741685186158</updated>
      <workItem from="1741685187371" duration="1461000" />
      <workItem from="1741686692758" duration="13926000" />
      <workItem from="1741831433729" duration="13905000" />
      <workItem from="1741921971243" duration="1479000" />
      <workItem from="1741925148776" duration="18135000" />
      <workItem from="1742176381872" duration="24320000" />
      <workItem from="1742264311812" duration="812000" />
      <workItem from="1742269118402" duration="231000" />
      <workItem from="1742269500599" duration="17442000" />
      <workItem from="1742350983029" duration="12105000" />
      <workItem from="1742367531472" duration="2126000" />
      <workItem from="1742379228083" duration="2235000" />
      <workItem from="1742440104575" duration="2398000" />
      <workItem from="1742444166358" duration="3105000" />
      <workItem from="1742523229289" duration="2683000" />
      <workItem from="1742782242290" duration="12762000" />
      <workItem from="1742881490771" duration="24905000" />
      <workItem from="1743065170089" duration="14696000" />
      <workItem from="1743388048708" duration="45948000" />
      <workItem from="1743905351746" duration="32844000" />
      <workItem from="1744083413457" duration="42875000" />
      <workItem from="1744509335611" duration="14873000" />
      <workItem from="1744691453872" duration="52916000" />
      <workItem from="1744954988301" duration="10070000" />
      <workItem from="1745207476307" duration="48239000" />
      <workItem from="1745471070561" duration="49505000" />
      <workItem from="1746589334702" duration="116000" />
      <workItem from="1746590437566" duration="201000" />
      <workItem from="1746591100894" duration="5675000" />
      <workItem from="1746599534225" duration="1926000" />
      <workItem from="1746602363571" duration="16643000" />
      <workItem from="1746859610613" duration="40346000" />
      <workItem from="1747307011523" duration="3265000" />
      <workItem from="1747361141039" duration="623000" />
      <workItem from="1747361913703" duration="14000" />
      <workItem from="1747362045520" duration="30076000" />
      <workItem from="1747654162006" duration="11513000" />
      <workItem from="1748072661038" duration="610000" />
      <workItem from="1748226469704" duration="17500000" />
      <workItem from="1748941054764" duration="4176000" />
      <workItem from="1749005406407" duration="15672000" />
      <workItem from="1749261488040" duration="10909000" />
      <workItem from="1749632614662" duration="61162000" />
      <workItem from="1750211788434" duration="13177000" />
    </task>
    <task id="LOCAL-00001" summary="新增订单和渠道接口">
      <option name="closed" value="true" />
      <created>1742271427690</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1742271427690</updated>
    </task>
    <task id="LOCAL-00002" summary="新增 订单统计">
      <option name="closed" value="true" />
      <created>1742366662395</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1742366662395</updated>
    </task>
    <task id="LOCAL-00003" summary="修改配置文件">
      <option name="closed" value="true" />
      <created>1742443730499</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1742443730499</updated>
    </task>
    <task id="LOCAL-00004" summary="修改配置文件">
      <option name="closed" value="true" />
      <created>1742805231682</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1742805231682</updated>
    </task>
    <task id="LOCAL-00005" summary="修改配置文件">
      <option name="closed" value="true" />
      <created>1742813269630</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1742813269630</updated>
    </task>
    <task id="LOCAL-00006" summary="支持多选">
      <option name="closed" value="true" />
      <created>1742882373681</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1742882373681</updated>
    </task>
    <task id="LOCAL-00007" summary="新增用户页面">
      <option name="closed" value="true" />
      <created>1743072714879</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1743072714879</updated>
    </task>
    <task id="LOCAL-00008" summary="修改">
      <option name="closed" value="true" />
      <created>1744105515844</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744105515844</updated>
    </task>
    <task id="LOCAL-00009" summary="修改">
      <option name="closed" value="true" />
      <created>1744107847020</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1744107847020</updated>
    </task>
    <task id="LOCAL-00010" summary="修改">
      <option name="closed" value="true" />
      <created>1744177519720</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1744177519720</updated>
    </task>
    <task id="LOCAL-00011" summary="修改">
      <option name="closed" value="true" />
      <created>1744265074045</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1744265074045</updated>
    </task>
    <task id="LOCAL-00012" summary="运营管理">
      <option name="closed" value="true" />
      <created>1744696811121</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1744696811121</updated>
    </task>
    <task id="LOCAL-00013" summary="修复问题">
      <option name="closed" value="true" />
      <created>1744698906479</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1744698906479</updated>
    </task>
    <task id="LOCAL-00014" summary="封禁用户功能">
      <option name="closed" value="true" />
      <created>1744707111913</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1744707111913</updated>
    </task>
    <task id="LOCAL-00015" summary="区服统计">
      <option name="closed" value="true" />
      <created>1744792377295</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1744792377295</updated>
    </task>
    <task id="LOCAL-00016" summary="修改角色查询权限">
      <option name="closed" value="true" />
      <created>1744874738349</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1744874738349</updated>
    </task>
    <task id="LOCAL-00017" summary="修改区服">
      <option name="closed" value="true" />
      <created>1744957546744</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1744957546744</updated>
    </task>
    <task id="LOCAL-00018" summary="修改错误">
      <option name="closed" value="true" />
      <created>1744958971951</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1744958971951</updated>
    </task>
    <task id="LOCAL-00019" summary="新增游戏管理">
      <option name="closed" value="true" />
      <created>1744967696928</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1744967696928</updated>
    </task>
    <task id="LOCAL-00020" summary="带队逻辑修改">
      <option name="closed" value="true" />
      <created>1745296448262</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1745296448262</updated>
    </task>
    <task id="LOCAL-00021" summary="带队逻辑修改">
      <option name="closed" value="true" />
      <created>1745297089295</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1745297089295</updated>
    </task>
    <task id="LOCAL-00022" summary=" 修改错误注册人数">
      <option name="closed" value="true" />
      <created>1745300428907</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1745300428907</updated>
    </task>
    <task id="LOCAL-00023" summary="修改权限">
      <option name="closed" value="true" />
      <created>1745393000989</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1745393000989</updated>
    </task>
    <task id="LOCAL-00024" summary="新增游戏管理">
      <option name="closed" value="true" />
      <created>1745488577659</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1745488577659</updated>
    </task>
    <task id="LOCAL-00025" summary="新增游戏管理">
      <option name="closed" value="true" />
      <created>1745488967252</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1745488967252</updated>
    </task>
    <task id="LOCAL-00026" summary="新增游戏管理">
      <option name="closed" value="true" />
      <created>1745835601565</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1745835601565</updated>
    </task>
    <task id="LOCAL-00027" summary="新增游戏管理">
      <option name="closed" value="true" />
      <created>1745836654410</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1745836654410</updated>
    </task>
    <task id="LOCAL-00028" summary="修改">
      <option name="closed" value="true" />
      <created>1745919127486</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1745919127486</updated>
    </task>
    <task id="LOCAL-00029" summary="修改">
      <option name="closed" value="true" />
      <created>1746508272940</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1746508272940</updated>
    </task>
    <task id="LOCAL-00030" summary="渠道管理">
      <option name="closed" value="true" />
      <created>1746520746216</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1746520746216</updated>
    </task>
    <task id="LOCAL-00031" summary="修改">
      <option name="closed" value="true" />
      <created>1746522263662</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1746522263662</updated>
    </task>
    <task id="LOCAL-00032" summary="分包管理">
      <option name="closed" value="true" />
      <created>1746700920397</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1746700920397</updated>
    </task>
    <task id="LOCAL-00033" summary="网站管理">
      <option name="closed" value="true" />
      <created>1747374156023</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1747374156023</updated>
    </task>
    <task id="LOCAL-00034" summary="网站管理">
      <option name="closed" value="true" />
      <created>1747547642541</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1747547642541</updated>
    </task>
    <task id="LOCAL-00035" summary="修复带队数据问题">
      <option name="closed" value="true" />
      <created>1747550383039</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1747550383039</updated>
    </task>
    <task id="LOCAL-00036" summary="修复带队数据问题">
      <option name="closed" value="true" />
      <created>1747655384180</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1747655384180</updated>
    </task>
    <task id="LOCAL-00037" summary="修复登录问题">
      <option name="closed" value="true" />
      <created>1748314871596</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1748314871596</updated>
    </task>
    <task id="LOCAL-00038" summary="修复问题">
      <option name="closed" value="true" />
      <created>1748938774920</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1748938774920</updated>
    </task>
    <task id="LOCAL-00039" summary="修复问题">
      <option name="closed" value="true" />
      <created>1749116753329</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1749116753329</updated>
    </task>
    <task id="LOCAL-00040" summary="支付">
      <option name="closed" value="true" />
      <created>1750053494975</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1750053494975</updated>
    </task>
    <task id="LOCAL-00041" summary="支付">
      <option name="closed" value="true" />
      <created>1750059915644</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1750059915644</updated>
    </task>
    <task id="LOCAL-00042" summary="支付">
      <option name="closed" value="true" />
      <created>1750157571728</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750157571728</updated>
    </task>
    <task id="LOCAL-00043" summary="支付">
      <option name="closed" value="true" />
      <created>1750158261067</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750158261067</updated>
    </task>
    <task id="LOCAL-00044" summary="修改配置">
      <option name="closed" value="true" />
      <created>1750158866752</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750158866752</updated>
    </task>
    <task id="LOCAL-00045" summary="支付">
      <option name="closed" value="true" />
      <created>1750214960342</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750214960342</updated>
    </task>
    <task id="LOCAL-00046" summary="支付">
      <option name="closed" value="true" />
      <created>1750246679024</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750246679024</updated>
    </task>
    <option name="localTasksCounter" value="47" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="新增订单和渠道接口" />
    <MESSAGE value="新增 订单统计" />
    <MESSAGE value="修改配置文件" />
    <MESSAGE value="支持多选" />
    <MESSAGE value="新增用户页面" />
    <MESSAGE value="运营管理" />
    <MESSAGE value="封禁用户功能" />
    <MESSAGE value="区服统计" />
    <MESSAGE value="修改角色查询权限" />
    <MESSAGE value="修改区服" />
    <MESSAGE value="修改错误" />
    <MESSAGE value="带队逻辑修改" />
    <MESSAGE value=" 修改错误注册人数" />
    <MESSAGE value="修改权限" />
    <MESSAGE value="新增游戏管理" />
    <MESSAGE value="渠道管理" />
    <MESSAGE value="修改" />
    <MESSAGE value="分包管理" />
    <MESSAGE value="网站管理" />
    <MESSAGE value="修复带队数据问题" />
    <MESSAGE value="修复登录问题" />
    <MESSAGE value="修复问题" />
    <MESSAGE value="修改配置" />
    <MESSAGE value="支付" />
    <option name="LAST_COMMIT_MESSAGE" value="支付" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/role/SdkRoleServiceImpl.java</url>
          <line>146</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java</url>
          <line>147</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java</url>
          <line>188</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java</url>
          <line>174</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java</url>
          <line>164</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java/com/mandong/api/module/erp/service/game/GameServiceImpl.java</url>
          <line>235</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>