package com.mandong.api.module.erp.service.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.channel.SdkChannelMapper;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserDeviceMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserLoginLogsMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.enums.ErrorCodeConstants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

/**
 * 游戏用户 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SdkUserServiceImpl implements SdkUserService {

    @Resource
    private SdkOrderMapper sdkOrderMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private SdkUserLoginLogsMapper sdkUserLoginLogsMapper;

    @Resource
    private SdkUserDeviceMapper sdkUserDeviceMapper;


    @Resource
    private SdkOptMapper sdkOptMapper;

    @Resource
    private SdkChannelMapper sdkChannelMapper;

    @Override
    public PageResult<UserPageRespVO> getUserPage(UserPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        // 如果是运营人员
        if (roleId.equals("163")){
            List<SdkOptLinkDO> optVOS = sdkOptMapper.selectLinks(userId);

            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                pageReqVO.getProductId().forEach(productId -> {
                    List<SdkOptLinkDO> list = optVOS.stream().filter(opt -> opt.getProductId().equals(productId)).toList();
                    if (list.isEmpty()) {
                        pageReqVO.setProductId(optVOS.stream().map(SdkOptLinkDO::getProductId).toList());
                    }
                });
            }else{
                pageReqVO.setProductId(optVOS.stream().map(SdkOptLinkDO::getProductId).toList());
            }
            if (pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty()) {
                pageReqVO.getChannelCode().forEach(channel -> {
                    List<SdkOptLinkDO> list = optVOS.stream().filter(opt -> opt.getChannelCode().equals(channel)).toList();
                    if (list.isEmpty()) {
                        pageReqVO.setChannelCode(optVOS.stream().map(SdkOptLinkDO::getChannelCode).toList());
                    }
                });
            }else {
                pageReqVO.setChannelCode(optVOS.stream().map(SdkOptLinkDO::getChannelCode).toList());
            }



        }



        return sdkUserMapper.selectPage(pageReqVO);
    }

    @Override
    public UserDetailRespVO getUserDetail(Long id) {


        return sdkUserMapper.getUserDetail(id);
    }

    @Override
    public PageResult<UserDevicePageRespVO> getUserDevicePage(UserPageReqVO pageReqVO) {

        return sdkUserDeviceMapper.selectUserDevicePage(pageReqVO);
    }

    @Override
    public PageResult<UserLoginPageRespVO> getUserLoginPage(UserPageReqVO pageReqVO) {
        return sdkUserLoginLogsMapper.selectUserLoginPage(pageReqVO);
    }

    @Override
    @DS("sdkDB")
    @DSTransactional(rollbackFor = Exception.class)
    public CommonResult transferChannel(SdkUserTransferReqVO reqVO) {
       try {
           SdkUserDO sdkUserDO = sdkUserMapper.selectById(reqVO.getUid());
           if (sdkUserDO == null) {
               return CommonResult.error(ErrorCodeConstants.USER_NOT_EXISTS);
           }
           sdkUserDO.setChannelCode(reqVO.getChannelCode());
           sdkUserDO.setProductId(reqVO.getProductId());
           sdkUserMapper.updateById(sdkUserDO);

           if (reqVO.getTransferRule() == 2){
               List<SdkOrderDO> SdkOrderDOs;
               if (reqVO.getTransferTime() != null){
                   SdkOrderDOs = sdkOrderMapper.selectList(new MPJLambdaQueryWrapper<SdkOrderDO>().selectAll(SdkOrderDO.class)
                           .eq(SdkOrderDO::getUid, reqVO.getUid())
                           .ge(SdkOrderDO::getCreateTime, reqVO.getTransferTime())
                   );

               }else {
                   SdkOrderDOs = sdkOrderMapper.selectList(SdkOrderDO::getUid, reqVO.getUid());
               }
               SdkOrderDOs.forEach(sdkOrderDO -> {
                   sdkOrderDO.setChannelCode(reqVO.getChannelCode());
                   sdkOrderDO.setProductId(reqVO.getProductId());
               });

               if (!SdkOrderDOs.isEmpty()){
                   sdkOrderMapper.updateBatch(SdkOrderDOs);
               }
           }
       }catch (Exception e) {
           log.error(e.getMessage(), e);
           throw e;
       }

        return CommonResult.success("转移成功");
    }

    @Override
    public void updateStatus(Long uid, Integer status) {

        SdkUserDO sdkUserDO = sdkUserMapper.selectById(uid);
        if (sdkUserDO == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        sdkUserDO.setUserStatus(status);
        sdkUserMapper.updateById(sdkUserDO);
        // TODO 删除SDK redis缓存

    }
}
