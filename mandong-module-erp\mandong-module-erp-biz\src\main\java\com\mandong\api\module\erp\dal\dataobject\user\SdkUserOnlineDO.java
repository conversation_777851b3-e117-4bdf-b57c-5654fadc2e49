package com.mandong.api.module.erp.dal.dataobject.user;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@TableName("qsdk_user_online")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkUserOnlineDO {
    /**
     *
     */
    private Integer id;

    /**
     *
     */
    @TableField("manageUid")
    private Integer manageUid;

    /**
     * 产品id
     */
    @TableField("productId")
    private Integer productId;

    /**
     * 用户id
     */
    @TableField("uid")
    private Integer uid;

    /**
     * 日期
     */
    @TableField("date")
    private Long date;

    /**
     * 创建时间
     */
    @TableField("createTime")
    private Integer createTime;

    /**
     * 设备id
     */
    @TableField("deviceId")
    private String deviceId;

    /**
     * 登录时间
     */
    @TableField("onlineTime")
    private Integer onlineTime;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;
}