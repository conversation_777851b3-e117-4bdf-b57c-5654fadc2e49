package com.mandong.api.module.erp.service.game;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface GameService {
    PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO);

    GameGetUrlRespVO getUrl(Integer id);


    List<GameVersionRespVO> getGameVersion(Integer id);

    PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO);

    List<SdkShortHostsDO> getShortHost();

    List<SdkAdPageDO> getAdPage(Integer id);

    UserDetailRespVO login(GameLoginReqVO reqVO);

    UserDetailRespVO googleLogin(GameGoogleLoginReqVO reqVO);

}
