package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户登录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserLoginPageRespVO {

    /**
     * 产品id
     */
    @Schema(description = "用户id", example = "22173")
    private String username;

    /**
     * 登录状态  0 正常 1账号不存在 2密码错误
     */
    @Schema(description = "0 正常 1账号不存在 2密码错误", example = "22173")
    private Integer loginStatus;

    /**
     * 登录时间
     */
    @Schema(description = "登录时间", example = "22173")
    private Long createTime;


    /**
     * 设备id
     */
    @Schema(description = "设备id", example = "22173")
    private String deviceId;

    /**
     * 登陆方式 1用户名  2自动登录 3短信 4游客
     */
    @Schema(description = "登陆方式", example = "22173")
    private Integer loginType;

    /**
     * sdk版本
     */
    @Schema(description = "sdk版本", example = "22173")
    private String sdkVersion;

    /**
     * 游戏版本
     */
    @Schema(description = "游戏版本", example = "22173")
    private String gameVersion;

    @Schema(description = "登录ip", example = "22173")
    private String loginIp;
    /**
     * 产品
     */
    @Schema(description = "产品", example = "22173")
    private String productName;

    @Schema(description = "运营人员", example = "22173")
    private String channelName;
}
