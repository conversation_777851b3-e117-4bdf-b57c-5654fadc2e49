package com.mandong.api.module.erp.convert.pay;

import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigMethodDO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigParamDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PayConfigConvert {

    PayConfigConvert INSTANCE = Mappers.getMapper(PayConfigConvert.class);

    default List<PayConfigVO> convertList(List<PayConfigDO> list, 
                                         Map<Long, List<PayConfigParamDO>> paramMap,
                                         Map<Long, List<PayConfigMethodDO>> methodMap) {
        return list.stream().map(config -> convert(config, 
                paramMap.get(config.getId()), 
                methodMap.get(config.getId()))).collect(Collectors.toList());
    }

    default PayConfigVO convert(PayConfigDO config, 
                               List<PayConfigParamDO> params, 
                               List<PayConfigMethodDO> methods) {
        PayConfigVO vo = BeanUtils.toBean(config, PayConfigVO.class);
        if (params != null) {
            vo.setParams(BeanUtils.toBean(params, com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigParamVO.class));
        }
        if (methods != null) {
            vo.setMethods(BeanUtils.toBean(methods, com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigMethodVO.class));
        }
        return vo;
    }

}
