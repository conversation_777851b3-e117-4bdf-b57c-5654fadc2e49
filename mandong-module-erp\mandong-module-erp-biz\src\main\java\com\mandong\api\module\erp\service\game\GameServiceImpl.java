package com.mandong.api.module.erp.service.game;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.*;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LOGIN_FAILED;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;
@Slf4j
@Service
public class GameServiceImpl implements GameService {

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkPackGameApkMapper sdkPackGameApkMapper;

    @Resource
    private SdkPackTaskMapper sdkPackTaskMapper;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private SdkAdPageMapper sdkAdPageMapper;

    @Resource
    private SdkShortHostsMapper sdkShortHostsMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${google.oauth.client-id}")
    private String googleClientId;

    @Value("${google.oauth.client-secret}")
    private String googleClientSecret;



    @Override
    public PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO) {
        return sdkProductMapper.selectPage(pageReqVO);
    }

    @Override
    public GameGetUrlRespVO getUrl(Integer id) {
        return null;
    }

    @Override
    public List<GameVersionRespVO> getGameVersion(Integer id) {

        return sdkPackGameApkMapper.selectGameVersion(id);
    }

    @Override
    public PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        List<SdkOperationCondition> operationPermittedConditions = null;
        // 如果是运营人员
        if (roleId.equals("163")) {
            // 获取用户权限范围内的产品和渠道条件
            operationPermittedConditions = orderService.getOperationUserPermittedConditions(userId);
            if (CollectionUtils.isEmpty(operationPermittedConditions)) {
                return new PageResult<>(new ArrayList<>(),0L);
            }
        }

        return sdkPackTaskMapper.getTaskPage(reqVO,operationPermittedConditions);
    }

    @Override
    public List<SdkShortHostsDO> getShortHost() {
        return sdkShortHostsMapper.selectList();
    }



    @Override
    public List<SdkAdPageDO> getAdPage(Integer id) {


        return sdkAdPageMapper.getAdPage(id);
    }

    @Override
    public UserDetailRespVO login(GameLoginReqVO reqVO) {

        String password = reqVO.getPassword();
        String username = reqVO.getUsername();
        String productId = reqVO.getProductId();

        String[] productIds = productId.split(",");


        SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUsername, username)
                .in(SdkUserDO::getProductId, Arrays.stream(productIds).toArray()));
        if (sdkUserDO == null) {
            throw exception(LOGIN_FAILED);
        }

        MD5 md5 = new MD5();
        String sPassword = md5.digestHex(password);
        String key = sPassword + sdkUserDO.getSlat();

        SdkUserDO userDO = sdkUserMapper.selectOne(SdkUserDO::getUsername, username, SdkUserDO::getPassword, md5.digestHex(key));
        if (userDO == null) {
            throw exception(LOGIN_FAILED);
        }

        SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, userDO.getProductId());
        UserDetailRespVO userDetailRespVO = BeanUtils.toBean(userDO, UserDetailRespVO.class);
        userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl()).setProductCode(sdkProductDO.getProductCode()).setCallbackKey(sdkProductDO.getCallbackKey());


        return userDetailRespVO;
    }

    @Override
    public UserDetailRespVO googleLogin(GameGoogleLoginReqVO reqVO) {
        try {
            // 1. 使用授权码获取访问令牌
            String accessToken = getGoogleAccessToken(reqVO.getCode());

            // 2. 使用访问令牌获取用户信息
            JsonNode userInfo = getGoogleUserInfo(accessToken);

            if (userInfo == null) {
                throw exception(LOGIN_FAILED);
            }

            // 3. 提取用户信息
            String googleId = userInfo.has("sub") ? userInfo.get("sub").asText() : null;
            String email = userInfo.has("email") ? userInfo.get("email").asText() : null;
            String name = userInfo.has("name") ? userInfo.get("name").asText() : null;

            if (googleId == null && email == null) {
                throw exception(LOGIN_FAILED);
            }

            // 4. 查找用户（优先使用email，其次使用googleId作为username）
            String username = email != null ? email : googleId;

            // 根据gameId获取产品信息
            String[] gameIds = reqVO.getGameId().split(",");

            SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>()
                    .eq(SdkUserDO::getUsername, username)
                    .in(SdkUserDO::getProductId, Arrays.stream(gameIds).toArray()));

            if (sdkUserDO == null) {
                // 如果用户不存在，可以选择创建新用户或返回错误
                // 这里选择返回错误，要求用户先注册
                throw exception(LOGIN_FAILED);
            }

            // 5. 构建返回结果
            SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, sdkUserDO.getProductId());
            UserDetailRespVO userDetailRespVO = BeanUtils.toBean(sdkUserDO, UserDetailRespVO.class);
            userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl())
                    .setProductCode(sdkProductDO.getProductCode())
                    .setCallbackKey(sdkProductDO.getCallbackKey());

            return userDetailRespVO;

        } catch (Exception e) {
            // 记录日志并抛出登录失败异常
            log.error(e.getMessage(), e);
            throw exception(LOGIN_FAILED);
        }
    }

    /**
     * 使用授权码获取Google访问令牌
     */
    private String getGoogleAccessToken(String code) {
        try {
            String tokenUrl = "https://oauth2.googleapis.com/token";

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String requestBody = "client_id=" + googleClientId +
                    "&client_secret=" + googleClientSecret +
                    "&code=" + code +
                    "&grant_type=authorization_code" +
                    "&redirect_uri=http://localhost:3000/auth/google/callback";

            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    tokenUrl, HttpMethod.POST, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                return jsonNode.get("access_token").asText();
            }

            return null;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * 使用访问令牌获取Google用户信息
     */
    private JsonNode getGoogleUserInfo(String accessToken) {
        try {
            String userInfoUrl = "https://www.googleapis.com/oauth2/v2/userinfo";

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<String> request = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    userInfoUrl, HttpMethod.GET, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return objectMapper.readTree(response.getBody());
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
