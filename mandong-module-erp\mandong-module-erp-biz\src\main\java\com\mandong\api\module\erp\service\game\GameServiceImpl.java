package com.mandong.api.module.erp.service.game;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.*;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import com.mandong.api.module.system.api.social.SocialUserApi;
import com.mandong.api.module.system.api.social.dto.SocialUserRespDTO;
import com.mandong.api.module.system.enums.social.SocialTypeEnum;
import com.mandong.api.framework.common.enums.UserTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LOGIN_FAILED;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

@Service
public class GameServiceImpl implements GameService {

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkPackGameApkMapper sdkPackGameApkMapper;

    @Resource
    private SdkPackTaskMapper sdkPackTaskMapper;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private SdkAdPageMapper sdkAdPageMapper;

    @Resource
    private SdkShortHostsMapper sdkShortHostsMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private SocialUserApi socialUserApi;



    @Override
    public PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO) {
        return sdkProductMapper.selectPage(pageReqVO);
    }

    @Override
    public GameGetUrlRespVO getUrl(Integer id) {
        return null;
    }

    @Override
    public List<GameVersionRespVO> getGameVersion(Integer id) {

        return sdkPackGameApkMapper.selectGameVersion(id);
    }

    @Override
    public PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        List<SdkOperationCondition> operationPermittedConditions = null;
        // 如果是运营人员
        if (roleId.equals("163")) {
            // 获取用户权限范围内的产品和渠道条件
            operationPermittedConditions = orderService.getOperationUserPermittedConditions(userId);
            if (CollectionUtils.isEmpty(operationPermittedConditions)) {
                return new PageResult<>(new ArrayList<>(),0L);
            }
        }

        return sdkPackTaskMapper.getTaskPage(reqVO,operationPermittedConditions);
    }

    @Override
    public List<SdkShortHostsDO> getShortHost() {
        return sdkShortHostsMapper.selectList();
    }



    @Override
    public List<SdkAdPageDO> getAdPage(Integer id) {


        return sdkAdPageMapper.getAdPage(id);
    }

    @Override
    public UserDetailRespVO login(GameLoginReqVO reqVO) {

        String password = reqVO.getPassword();
        String username = reqVO.getUsername();
        String productId = reqVO.getProductId();

        String[] productIds = productId.split(",");


        SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUsername, username)
                .in(SdkUserDO::getProductId, Arrays.stream(productIds).toArray()));
        if (sdkUserDO == null) {
            throw exception(LOGIN_FAILED);
        }

        MD5 md5 = new MD5();
        String sPassword = md5.digestHex(password);
        String key = sPassword + sdkUserDO.getSlat();

        SdkUserDO userDO = sdkUserMapper.selectOne(SdkUserDO::getUsername, username, SdkUserDO::getPassword, md5.digestHex(key));
        if (userDO == null) {
            throw exception(LOGIN_FAILED);
        }

        SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, userDO.getProductId());
        UserDetailRespVO userDetailRespVO = BeanUtils.toBean(userDO, UserDetailRespVO.class);
        userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl()).setProductCode(sdkProductDO.getProductCode()).setCallbackKey(sdkProductDO.getCallbackKey());


        return userDetailRespVO;
    }

    @Override
    public UserDetailRespVO googleLogin(GameGoogleLoginReqVO reqVO) {
        try {
            // 1. 使用Google授权码获取社交用户信息
            SocialUserRespDTO socialUser = socialUserApi.getSocialUserByCode(
                    UserTypeEnum.ADMIN.getValue(),
                    SocialTypeEnum.GOOGLE.getType(),
                    reqVO.getCode(),
                    "");

            if (socialUser == null || socialUser.getOpenid() == null) {
                throw exception(LOGIN_FAILED);
            }

            // 2. 根据Google用户信息查找用户
            // 这里我们使用openid作为用户名来查找用户
            String username = socialUser.getOpenid();

            // 根据gameId获取产品信息
            String[] gameIds = reqVO.getGameId().split(",");

            SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>()
                    .eq(SdkUserDO::getUsername, username)
                    .in(SdkUserDO::getProductId, Arrays.stream(gameIds).toArray()));

            if (sdkUserDO == null) {
                // 如果用户不存在，可以选择创建新用户或返回错误
                // 这里选择返回错误，要求用户先注册
                throw exception(LOGIN_FAILED);
            }

            // 3. 构建返回结果
            SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, sdkUserDO.getProductId());
            UserDetailRespVO userDetailRespVO = BeanUtils.toBean(sdkUserDO, UserDetailRespVO.class);
            userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl())
                    .setProductCode(sdkProductDO.getProductCode())
                    .setCallbackKey(sdkProductDO.getCallbackKey());

            return userDetailRespVO;

        } catch (Exception e) {
            // 记录日志并抛出登录失败异常
            throw exception(LOGIN_FAILED);
        }
    }
}
