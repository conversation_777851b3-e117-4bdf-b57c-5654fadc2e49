package com.mandong.api.module.erp.dal.dataobject.role;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_roles")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkRoleDO {

    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 渠道code
     */
    @TableField("channelCode")
    private String channelCode;

    /**
     *  游戏id
     */
    @TableField("productId")
    private Long productId;


    /**
     * 区服
     */
    @TableField("serverName")
    private String serverName;

    /**
     * 用户名
     */
    @TableField("userName")
    private String userName;

    /**
     * 角色名
     */
    @TableField("gameRoleName")
    private String gameRoleName;

    /**
     * 角色id
     */
    @TableField("gameRoleId")
    private String gameRoleId;

    /**
     * vip等级
     */
    @TableField("vipLevel")
    private String vipLevel;

    /**
     * 角色等级
     */
    @TableField("gameRoleLevel")
    private String gameRoleLevel;



    /**
     * 最后登录时间
     */
    @TableField("lastLoginTime")
    private Long lastLoginTime;

    /**
     * 登录次数
     */
    @TableField("loginNum")
    private int loginNum;

    /**
     * 消费金额
     */
    @TableField("payAmount")
    private Float payAmount;

    /**
     * 消费次数
     */
    @TableField("payNum")
    private int payNum;
    /**
     * 创建日期
     */
    @TableField("createTime")
    private Long createTime;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;


    /**
     * 设备id
     */
    @TableField("deviceId")
    private String deviceId;

}
