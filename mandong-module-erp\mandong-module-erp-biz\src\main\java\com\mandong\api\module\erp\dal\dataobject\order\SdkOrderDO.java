package com.mandong.api.module.erp.dal.dataobject.order;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mandong.api.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@TableName("qsdk_order")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
public class SdkOrderDO    {
    @TableField("manageUid")
    private Long manageUid;
    /**
     * 主键
     */
    private Long id;
    @TableField("uid")
    private Long uid;
    /**
     * 订单号
     */
    @TableField("orderNo")
    private String orderNo;

    @TableField("channelCode")
    private String channelCode;
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    /**
     * 角色名
     */
    @TableField("roleName")
    private String roleName;
    /**
     * 角色id
     */
    @TableField("roleId")
    private String roleId;
    /**
     * 角色等级
     */
    @TableField("roleLevel")
    private String roleLevel;
    /**
     * 区服
     */
    @TableField("serverName")
    private String serverName;
    /**
     * 金额
     */
    @TableField("dealAmount")
    private Float dealAmount;

    @TableField("dealUsdAmount")
    private Float dealUsdAmount;
    /**
     * 游戏id
     */
    @TableField("productId")
    private Long productId;
    /**
     * 付款时间
     */
    @TableField("payTime")
    private Long payTime;

    @TableField("createTime")
    private Long createTime;
    /**
     * 付款状态
     */
    @TableField("payStatus")
    private String payStatus;

    @TableField("payType")
    private Integer payType;

    @TableField("asyncStatus")
    private String asyncStatus;

    @TableField("currencyWord")
    private String currencyWord;

    @TableField("currencyCode")
    private String currencyCode;

    @TableField("amount")
    private Float amount;

    @TableField("usdAmount")
    private Float usdAmount;

    @TableField("channelOrderNo")
    private String channelOrderNo;
    @TableField("orderGoodsId")
    private String orderGoodsId;

    @TableField("orderSubject")
    private String orderSubject;

    @TableField("callbackUrl")
    private String callbackUrl;

    @TableField("extrasParams")
    private String extrasParams;

    @TableField("payMessage")
    private String payMessage;
    @TableField("actRate")
    private Float actRate;
}
