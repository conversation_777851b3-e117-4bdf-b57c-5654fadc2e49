package com.mandong.api.module.erp.controller.admin.game.vo;

import com.mandong.api.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GameTaskListReqVO  extends PageParam {

    private Long productId;

    private List<String> channelCode;


}
