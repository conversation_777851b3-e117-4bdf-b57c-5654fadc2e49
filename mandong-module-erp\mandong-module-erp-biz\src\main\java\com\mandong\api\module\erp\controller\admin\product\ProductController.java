package com.mandong.api.module.erp.controller.admin.product;

import com.mandong.api.framework.apilog.core.annotation.ApiAccessLog;
import com.mandong.api.framework.common.enums.CommonStatusEnum;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageParam;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.excel.core.util.ExcelUtils;
import com.mandong.api.module.erp.controller.admin.product.vo.product.ErpProductPageReqVO;
import com.mandong.api.module.erp.controller.admin.product.vo.product.ErpProductRespVO;
import com.mandong.api.module.erp.controller.admin.product.vo.product.ProductSaveReqVO;
import com.mandong.api.module.erp.dal.dataobject.product.ErpProductDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.service.product.ErpProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.mandong.api.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.mandong.api.framework.common.pojo.CommonResult.success;
import static com.mandong.api.framework.common.util.collection.CollectionUtils.convertList;

@Tag(name = "管理后台 - ERP 游戏")
@RestController
@RequestMapping("/erp/product")
@Validated
public class ProductController {

    @Resource
    private ErpProductService productService;







    @GetMapping("/list")
    @Operation(summary = "获得产品")
    public CommonResult<List<SdkProductDO>> getProduct() {
        return success(productService.getProductList());
    }




}