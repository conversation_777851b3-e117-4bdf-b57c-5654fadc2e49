package com.mandong.api.module.erp.controller.admin.channel.vo.channel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 订单 DO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
public class SdkChannelVO {

    private Long id;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 渠道名
     */
    private String channelName;
    /**
     *  游戏id
     */
    private Long productId;

    /**
     * 游戏名
     */
    private String productName;



}
