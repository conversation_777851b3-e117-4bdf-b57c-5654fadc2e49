package com.mandong.api.module.erp.controller.admin.role.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * SDK订单条件 VO
 * 
 * <AUTHOR>
 */
@Schema(description = "SDK产品和渠道条件")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SdkRoleCondition {
    
    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Integer productId;
    
    /**
     * 区服名称
     */
    @Schema(description = "区服名称")
    private String channelCode;
    

} 