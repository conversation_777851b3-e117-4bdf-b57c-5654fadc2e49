package com.mandong.api.module.erp.dal.dataobject.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 支付方式表
 */
@Data
@TableName(value = "pay_method")
public class PayMethodDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "type")
    private String type;
    /**
     * 支付方式代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 支付方式名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 支付提供商(PAYERMAX, GOOGLE_PAY)
     */
    @TableField(value = "provider")
    private String provider;

    /**
     * 图标URL
     */
    @TableField(value = "icon_url")
    private String iconUrl;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField(value = "`status`")
    private Boolean status;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
}