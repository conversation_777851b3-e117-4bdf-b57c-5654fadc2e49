package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "支付页面 - 支付配置 Response VO")
@Data
public class PayConfigForPaymentVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝支付")
    private String name;

    @Schema(description = "支付提供商", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    private String provider;


    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "支付方式列表")
    private List<PayConfigMethodForPaymentVO> methods;

}
