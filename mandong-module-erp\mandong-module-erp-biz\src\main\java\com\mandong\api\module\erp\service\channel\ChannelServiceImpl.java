package com.mandong.api.module.erp.service.channel;

import cn.hutool.core.date.DateTime;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageParam;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.ChannelGetListRespVO;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.SdkChannelVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.sdkMysql.channel.SdkChannelMapper;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;
import static com.mandong.api.module.system.enums.ErrorCodeConstants.USER_CHANNEL_EXISTS;

/**
 * 渠道 service
 */
@Service
@Validated
public class ChannelServiceImpl implements ChannelService {
    @Resource
    private SdkChannelMapper sdkChannelMapper;

    @Resource
    private SdkOrderMapper sdkOrderMapper;
    @Override
    public PageResult<SdkChannelVO> getChannel(ChannelGetListRespVO channelGetListRespVO) {
        channelGetListRespVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        return sdkChannelMapper.selectPage(channelGetListRespVO);
    }

    @Override
    public SdkChannelDO getChannel(Long id) {
        return sdkChannelMapper.selectById(id);
    }

    @Override
    public Integer deleteChannel(Long id) {
        SdkChannelDO sdkChannelDO = sdkChannelMapper.selectById(id);


        Long count = sdkOrderMapper.selectCount(new LambdaQueryWrapperX<SdkOrderDO>().eq(SdkOrderDO::getProductId, sdkChannelDO.getProductId())
                .eq(SdkOrderDO::getChannelCode, sdkChannelDO.getChannelCode()));
        if (count > 0) {
            throw exception(CHANNEL_ORDER_EXISTS);
        }

        return sdkChannelMapper.deleteById(id);
    }

    @Override
    public PageResult<SdkChannelVO> getChannelPage(ChannelGetListRespVO channelGetListRespVO) {
        return sdkChannelMapper.selectPage(channelGetListRespVO);
    }

    @Override
    @DS("sdkDB")
    @DSTransactional(rollbackFor = Exception.class)
    public Long addChannel(SdkChannelVO sdkChannelVO) {

        List<SdkChannelDO> sdkChannelDOS = sdkChannelMapper.selectList(SdkChannelDO::getChannelCode, sdkChannelVO.getChannelCode(), SdkChannelDO::getProductId, sdkChannelVO.getProductId());
        if (!sdkChannelDOS.isEmpty()) {
            throw exception(CHANNEL_EXISTS);
        }

        SdkChannelDO sdkChannelDO = BeanUtils.toBean(sdkChannelVO, SdkChannelDO.class);
        sdkChannelDO.setCreateTime(new DateTime().toTimestamp().getTime()/1000);
        sdkChannelMapper.insert(sdkChannelDO);

        return sdkChannelDO.getId();
    }

    @Override
    public Long editChannel(SdkChannelVO sdkChannelVO) {
        SdkChannelDO sdkChannelDO = sdkChannelMapper.selectById(sdkChannelVO.getId());
        if (sdkChannelDO == null) {
            throw exception(CHANNEL_NOT_EXISTS);
        }

        sdkChannelDO.setChannelName(sdkChannelVO.getChannelName());
        sdkChannelMapper.updateById(sdkChannelDO);

        return 0L;
    }
}
