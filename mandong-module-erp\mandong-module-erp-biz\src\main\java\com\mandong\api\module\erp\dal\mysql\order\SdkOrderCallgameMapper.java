package com.mandong.api.module.erp.dal.mysql.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderCallgame;
import org.apache.ibatis.annotations.Mapper;
@DS("sdkDB")
@Mapper
public interface SdkOrderCallgameMapper extends BaseMapperX<SdkOrderCallgame> {
}