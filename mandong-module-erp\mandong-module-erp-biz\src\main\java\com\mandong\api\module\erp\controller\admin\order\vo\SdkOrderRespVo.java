package com.mandong.api.module.erp.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.mandong.api.framework.excel.core.annotations.DictFormat;
import com.mandong.api.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - Sdk订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkOrderRespVo {

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String orderNo;
    /**
     * 渠道名称
     */
    @ExcelProperty("渠道名称")
    private String channelName;

    /**
     * 运营人员
     */
    @ExcelProperty("运营人员")
    private String optName;
    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    private String username;
    /**
     * 角色名
     */
    @ExcelProperty("角色名")
    private String roleName;
    /**
     * 角色id
     */
    @ExcelProperty("角色id")
    private String roleId;
    /**
     * 角色等级
     */
    @ExcelProperty("角色等级")
    private String roleLevel;
    /**
     * 区服
     */
    @ExcelProperty("区服")
    private String serverName;
    /**
     * 金额
     */
    @ExcelProperty("金额")
    private float dealAmount;
    /**
     * 游戏名称
     */
    @ExcelProperty("游戏名")
    private String productName;
    /**
     * 付款时间
     */
    @ExcelProperty("付款时间")
    private Long payTime;
    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private Long createTime;
    /**
     * 付款状态
     */
    @ExcelProperty(value = "付款状态",converter = DictConvert.class)
    @DictFormat("erp_order_status")
    private String payStatus;

    /**
     * 支付方式
     */
    @ExcelProperty("支付方式")
    private String payName;

    /**
     * 订单来源
     */
    @ExcelProperty(value = "订单来源", converter = DictConvert.class)
    @DictFormat("erp_order_source")
    private int orderSource = 2;

    private Long uid;
}
