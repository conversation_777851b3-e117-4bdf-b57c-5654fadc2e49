package com.mandong.api.module.erp.service.pay.dto.payermax;

import lombok.Data;

import java.util.List;

/**
 * PayerMax支付数据DTO
 *
 * <AUTHOR>
 */
@Data
public class PayerMaxDataDTO {
    
    /**
     * 商户订单号
     */
    private String outTradeNo;
    
    /**
     * 商品标题
     */
    private String subject;
    
    /**
     * 支付金额
     */
    private Integer totalAmount;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 国家代码
     */
    private String country;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 参考信息
     */
    private String reference;
    
    /**
     * 前端回调地址
     */
    private String frontCallbackUrl;
    
    /**
     * 异步通知地址
     */
    private String notifyUrl;
    
    /**
     * 集成方式
     */
    private String integrate = "Hosted_Checkout";
    
    /**
     * 过期时间（秒）
     */
    private String expireTime = "1800";
    
    /**
     * 支付详情
     */
    private PayerMaxPaymentDetailDTO paymentDetail;
    
    /**
     * 环境信息
     */
    private PayerMaxEnvInfoDTO envInfo;
    
    /**
     * 商品详情列表
     */
    private List<PayerMaxGoodsDetailDTO> goodsDetails;
    
    /**
     * 收货信息
     */
    private PayerMaxShippingInfoDTO shippingInfo;
    
    /**
     * 账单信息
     */
    private PayerMaxBillingInfoDTO billingInfo;
    
    /**
     * 风险参数
     */
    private PayerMaxRiskParamsDTO riskParams;
}
