package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - Sdk用户注册统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkUserSummaryMonthRespVO {
    @Schema(description = "日期")
    private String time;
    @Schema(description = "注册用户数")
    private Float price;
} 