package com.mandong.api.module.erp.controller.app.pay.vo;

import lombok.Data;

import java.util.List;

@Data
public class CallBackPayerMaxRespVO {
    private String code;
    private String msg;
    private String keyVersion;
    private String appId;
    private String merchantNo;
    private String notifyTime;
    private String notifyType;
    private Data data;

    @lombok.Data
    public static class Data {
        private String outTradeNo;
        private String tradeToken;
        private Float totalAmount;
        private String currency;
        private String country;
        private String status;
        private String completeTime;
        private List<PaymentDetail> paymentDetails;
        private String reference;
    }

    @lombok.Data
    public static class PaymentDetail {
        private String paymentMethodType;
        private String targetOrg;
        private String paymentTokenID;
    }
}
