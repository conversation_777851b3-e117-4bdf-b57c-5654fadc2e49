<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.site.SitePayLoginConfigMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.site.SitePayLoginConfig">
    <!--@mbg.generated-->
    <!--@Table site_pay_login_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="site_pay_id" jdbcType="BIGINT" property="sitePayId" />
    <result column="type" jdbcType="BIGINT" property="type" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, site_pay_id, `type`, client_id, creator, create_time, updater, update_time, deleted
  </sql>
</mapper>