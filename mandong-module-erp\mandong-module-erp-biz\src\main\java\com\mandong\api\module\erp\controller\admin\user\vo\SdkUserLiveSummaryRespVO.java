package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - Sdk订单交易统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SdkUserLiveSummaryRespVO {

    @Schema(description = "今日活跃数")
    private Long todayLive;
    @Schema(description = "当周活跃数")
    private Long weekLive;

    @Schema(description = "月活跃数")
    private List<SdkOrderSummaryMonthRespVO> monthlyLive;

}
