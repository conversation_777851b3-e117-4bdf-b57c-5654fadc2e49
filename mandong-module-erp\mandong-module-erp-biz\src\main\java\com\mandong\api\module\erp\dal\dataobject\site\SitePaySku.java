package com.mandong.api.module.erp.dal.dataobject.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

/**
 * 支付网站sku
 */
@Data
@TableName(value = "site_pay_sku")
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SitePaySku {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付网站id
     */
    @TableField(value = "site_id")
    private Long siteId;
    /**
     * 商品类型(0：货币  1：礼包)
     */
    @TableField(value = "sku_type")
    private int skuType;

    /**
     * 商品id
     */
    @TableField(value = "sku_code")
    private String skuCode;

    /**
     * 商品名称
     */
    @TableField(value = "sku_name")
    private String skuName;

    /**
     * 商品价格
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 币别 默认KRW
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * 商品图片地址
     */
    @TableField(value = "img_url")
    private String imgUrl;

    /**
     * sku状态（0正常 1停用）
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
}