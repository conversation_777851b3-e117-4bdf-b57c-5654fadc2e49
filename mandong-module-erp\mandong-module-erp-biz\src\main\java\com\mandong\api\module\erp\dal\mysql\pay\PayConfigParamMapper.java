package com.mandong.api.module.erp.dal.mysql.pay;

import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigParamDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支付配置参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PayConfigParamMapper extends BaseMapperX<PayConfigParamDO> {

    default List<PayConfigParamDO> selectListByConfigId(Long configId) {
        return selectList(new LambdaQueryWrapperX<PayConfigParamDO>()
                .eq(PayConfigParamDO::getConfigId, configId));
    }

    default void deleteByConfigId(Long configId) {
        delete(new LambdaQueryWrapperX<PayConfigParamDO>()
                .eq(PayConfigParamDO::getConfigId, configId));
    }

}