package com.mandong.api.module.erp.service.pay;

import cn.hutool.json.JSONUtil;
import com.mandong.api.module.erp.service.pay.dto.payermax.PayerMaxDataDTO;
import com.mandong.api.module.erp.service.pay.dto.payermax.PayerMaxRequestDTO;
import com.mandong.api.module.erp.service.pay.util.PayerMaxSignatureUtil;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * PayerMax签名功能演示
 * 
 * 这个类演示了如何正确使用PayerMax签名功能
 *
 * <AUTHOR>
 */
public class PayerMaxSignatureDemo {
    
    public static void main(String[] args) {
        try {
            // 1. 生成测试用的RSA密钥对（实际使用中应该使用PayerMax提供的密钥）
            System.out.println("=== 生成测试密钥对 ===");
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            
            PrivateKey privateKey = keyPair.getPrivate();
            PublicKey publicKey = keyPair.getPublic();
            
            String privateKeyStr = "-----BEGIN PRIVATE KEY-----\n" + 
                                  Base64.getEncoder().encodeToString(privateKey.getEncoded()) + 
                                  "\n-----END PRIVATE KEY-----";
            String publicKeyStr = "-----BEGIN PUBLIC KEY-----\n" + 
                                 Base64.getEncoder().encodeToString(publicKey.getEncoded()) + 
                                 "\n-----END PUBLIC KEY-----";
            
            System.out.println("私钥生成完成，长度: " + privateKeyStr.length());
            System.out.println("公钥生成完成，长度: " + publicKeyStr.length());
            
            // 2. 构建PayerMax请求数据
            System.out.println("\n=== 构建PayerMax请求数据 ===");
            PayerMaxRequestDTO request = buildTestRequest();
            
            // 3. 转换为JSON字符串（注意：这个字符串必须与HTTP body完全一致）
            String requestJson = JSONUtil.toJsonStr(request);
            System.out.println("请求JSON: " + requestJson);
            
            // 4. 生成签名
            System.out.println("\n=== 生成签名 ===");
            String signature = PayerMaxSignatureUtil.generateSignature(requestJson, privateKeyStr);
            System.out.println("生成的签名: " + signature);
            System.out.println("签名长度: " + signature.length());
            
            // 5. 验证签名
            System.out.println("\n=== 验证签名 ===");
            boolean isValid = PayerMaxSignatureUtil.verifySignature(requestJson, signature, publicKeyStr);
            System.out.println("签名验证结果: " + (isValid ? "✅ 成功" : "❌ 失败"));
            
            // 6. 测试错误数据的签名验证
            System.out.println("\n=== 测试错误数据验证 ===");
            String wrongData = "{\"wrong\":\"data\"}";
            boolean isInvalid = PayerMaxSignatureUtil.verifySignature(wrongData, signature, publicKeyStr);
            System.out.println("错误数据验证结果: " + (isInvalid ? "❌ 意外成功" : "✅ 正确失败"));
            
            // 7. 测试JSON格式一致性的重要性
            System.out.println("\n=== 测试JSON格式一致性 ===");
            demonstrateJsonConsistency(privateKeyStr);
            
            // 8. 验证密钥格式
            System.out.println("\n=== 验证密钥格式 ===");
            boolean keyFormatValid = PayerMaxSignatureUtil.validateKeyFormat(privateKeyStr, publicKeyStr);
            System.out.println("密钥格式验证: " + (keyFormatValid ? "✅ 正确" : "❌ 错误"));
            
            // 9. 演示正确的PayerMax请求方式
            System.out.println("\n=== PayerMax请求方式演示 ===");
            demonstratePayerMaxRequest(requestJson, signature);

            System.out.println("\n=== 演示完成 ===");
            System.out.println("PayerMax签名功能工作正常！");

        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 构建测试用的PayerMax请求
     */
    private static PayerMaxRequestDTO buildTestRequest() {
        PayerMaxRequestDTO request = new PayerMaxRequestDTO();
        request.setVersion("1.4");
        request.setKeyVersion("1");
        request.setRequestTime(LocalDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        request.setAppId("test_app_id_123456");
        request.setMerchantNo("TEST_MERCHANT_001");
        
        PayerMaxDataDTO data = new PayerMaxDataDTO();
        data.setOutTradeNo("ORDER_" + System.currentTimeMillis());
        data.setSubject("测试商品");
        data.setTotalAmount(10000); // 100.00元，以分为单位
        data.setCurrency("TWD");
        data.setCountry("TW");
        data.setUserId("test_user_001");
        data.setLanguage("zh_CN");
        data.setReference("测试支付订单");
        data.setFrontCallbackUrl("https://example.com/return");
        data.setNotifyUrl("https://example.com/notify");
        
        request.setData(data);
        return request;
    }
    
    /**
     * 演示JSON格式一致性的重要性
     */
    private static void demonstrateJsonConsistency(String privateKey) {
        try {
            // 创建相同内容但可能格式不同的JSON
            String json1 = "{\"key1\":\"val1\",\"key2\":\"val2\",\"key3\":\"val3\"}";
            String json2 = "{\n  \"key1\": \"val1\",\n  \"key2\": \"val2\",\n  \"key3\": \"val3\"\n}";
            
            System.out.println("JSON1 (压缩): " + json1);
            System.out.println("JSON2 (格式化): " + json2);
            
            String sign1 = PayerMaxSignatureUtil.generateSignature(json1, privateKey);
            String sign2 = PayerMaxSignatureUtil.generateSignature(json2, privateKey);
            
            System.out.println("签名1: " + sign1.substring(0, 20) + "...");
            System.out.println("签名2: " + sign2.substring(0, 20) + "...");
            System.out.println("签名是否相同: " + (sign1.equals(sign2) ? "✅ 相同" : "❌ 不同"));
            
            if (!sign1.equals(sign2)) {
                System.out.println("⚠️  这就是为什么PayerMax要求JSON格式必须一致的原因！");
            }
            
        } catch (Exception e) {
            System.err.println("JSON一致性演示失败: " + e.getMessage());
        }
    }

    /**
     * 演示PayerMax的正确请求方式
     */
    private static void demonstratePayerMaxRequest(String requestJson, String signature) {
        System.out.println("PayerMax请求方式说明：");
        System.out.println("1. 签名(sign)作为URL参数传递");
        System.out.println("2. 请求体为JSON格式");
        System.out.println("3. Content-Type: application/json");
        System.out.println();

        String apiUrl = "https://pay-gate.payermax.com/aggregate-pay/api/gateway/orderAndPay";
        String urlWithSign = apiUrl + "?sign=" + signature.substring(0, 20) + "...";

        System.out.println("请求URL: " + urlWithSign);
        System.out.println("请求方法: POST");
        System.out.println("请求头: Content-Type: application/json");
        System.out.println("请求体: " + requestJson);
        System.out.println();
        System.out.println("⚠️  重要：签名是基于请求体JSON字符串生成的，");
        System.out.println("   但签名本身作为URL参数传递，不包含在请求体中！");
    }
}
