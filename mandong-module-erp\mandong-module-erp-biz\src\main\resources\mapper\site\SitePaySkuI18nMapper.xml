<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.site.SitePaySkuI18nMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.site.SitePaySkuI18n">
    <!--@mbg.generated-->
    <!--@Table site_pay_sku_i18n-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sku_id, `locale`, title, description, create_time, updater, update_time, deleted, 
    tenant_id
  </sql>
</mapper>