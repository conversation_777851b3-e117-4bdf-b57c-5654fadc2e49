package com.mandong.api.module.erp.dal.sdkMysql.config;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkPayslMapper extends BaseMapperX<SdkPaysDO> {



}