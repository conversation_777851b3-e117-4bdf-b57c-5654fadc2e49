package com.mandong.api.module.erp.controller.admin.site.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayI18n;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 支付网站表
 */
@Data
public class SitePayPageRespVO {
    /**
     * 支付网站id
     */
    private Long id;

    /**
     * 网站名称
     */
    private String name;

    /**
     * 产品id
     */
    private String productId;

    private String productName;

    /**
     * 网站地址
     */
    private String url;

    /**
     * icon图
     */
    private String iconUrl;

    /**
     * banner图
     */
    private String bannerUrl;

    /**
     * 评分 5分制
     */
    private BigDecimal rating;

    /**
     * 玩家人数
     */
    private String playerCount;

    /**
     * 网站状态（0正常 1停用）
     */
    private Byte status;

    /**
     * 回调接口
     */
    private String callBackUrl;

    /**
     * 区服接口
     */
    private String serverUrl;

    /**
     * 登录接口
     */
    private String loginUrl;

    /**
     * 支付接口
     */
    private String payUrl;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;

    private List<SitePayI18n> i18ns;
}