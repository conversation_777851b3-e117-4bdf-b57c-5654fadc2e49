package com.mandong.api.module.erp.util;

import com.alibaba.fastjson.JSONObject;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;

/**
 * 多语言辅助工具类
 */
public class I18nHelper {
    /**
     * 从JSON字符串中提取当前语言环境的文本
     *
     * @param i18nJsonStr 多语言JSON字符串
     * @param field       字段名（如title、description）
     * @return 当前语言的文本，找不到返回null
     */
    public static String getCurrentText(String i18nJsonStr, String field) {
        if (i18nJsonStr == null || i18nJsonStr.isEmpty()) {
            return null;
        }
        
        JSONObject i18nObj = JsonParseUtil.parseObject(i18nJsonStr);
        if (i18nObj == null) {
            return null;
        }
        
        // 获取当前语言代码
        String locale = LocaleContextHolder.getLocale().getLanguage();
        
        // 如果当前语言不可用，尝试英语，然后中文
        return getTextWithFallback(i18nObj, field, locale, "en", "zh");
    }
    
    /**
     * 从JSON对象中提取指定语言的文本，支持回退语言
     *
     * @param i18nObj        多语言JSON对象
     * @param field          字段名（如title、description）
     * @param locale         首选语言代码
     * @param fallbackLocale 如果首选语言不可用，使用的回退语言代码
     * @return 对应语言的文本，找不到返回null
     */
    public static String getTextWithFallback(JSONObject i18nObj, String field, String locale, String... fallbackLocales) {
        // 尝试使用首选语言
        String text = JsonParseUtil.getI18nValue(i18nObj, field, locale);
        if (text != null) {
            return text;
        }
        
        // 依次尝试回退语言
        for (String fallbackLocale : fallbackLocales) {
            text = JsonParseUtil.getI18nValue(i18nObj, field, fallbackLocale);
            if (text != null) {
                return text;
            }
        }
        
        // 所有语言都没找到，返回null
        return null;
    }
    
    /**
     * 从Map中获取当前语言的文本
     *
     * @param i18nMap 多语言Map，key为语言代码，value为对应文本
     * @return 当前语言的文本，找不到返回null
     */
    public static String getCurrentText(Map<String, String> i18nMap) {
        if (i18nMap == null || i18nMap.isEmpty()) {
            return null;
        }
        
        // 获取当前语言代码
        String locale = LocaleContextHolder.getLocale().getLanguage();
        
        // 尝试获取当前语言
        String text = i18nMap.get(locale);
        if (text != null) {
            return text;
        }
        
        // 依次尝试英语、中文
        text = i18nMap.get("en");
        if (text != null) {
            return text;
        }
        
        text = i18nMap.get("zh");
        if (text != null) {
            return text;
        }
        
        // 所有语言都没找到，返回第一个可用的文本
        return i18nMap.values().iterator().next();
    }
    
    /**
     * 将多语言JSON对象解析为Map结构，便于前端使用
     * 
     * @param i18nJsonStr 多语言JSON字符串
     * @param fields 需要解析的字段列表，如title、description、category等
     * @param locales 需要解析的语言列表，如zh、en、ko等
     * @return 解析结果，格式为Map<字段名, Map<语言代码, 文本值>>
     */
    public static Map<String, Map<String, String>> parseI18nObject(String i18nJsonStr, String[] fields, String[] locales) {
        if (i18nJsonStr == null || i18nJsonStr.isEmpty()) {
            return null;
        }
        
        JSONObject i18nObj = JsonParseUtil.parseObject(i18nJsonStr);
        if (i18nObj == null) {
            return null;
        }
        
        Map<String, Map<String, String>> result = new HashMap<>();
        
        // 遍历每个字段
        for (String field : fields) {
            if (i18nObj.containsKey(field)) {
                JSONObject fieldObj = i18nObj.getJSONObject(field);
                Map<String, String> localeMap = new HashMap<>();
                
                // 遍历每种语言
                for (String locale : locales) {
                    if (fieldObj.containsKey(locale)) {
                        localeMap.put(locale, fieldObj.getString(locale));
                    }
                }
                
                // 只有当有语言数据时才添加该字段
                if (!localeMap.isEmpty()) {
                    result.put(field, localeMap);
                }
            }
        }
        
        return result.isEmpty() ? null : result;
    }
    
    /**
     * 将多语言JSON对象解析为Map结构，使用默认的字段和语言列表
     * 
     * @param i18nJsonStr 多语言JSON字符串
     * @return 解析结果，格式为Map<字段名, Map<语言代码, 文本值>>
     */
    public static Map<String, Map<String, String>> parseI18nObject(String i18nJsonStr) {
        // 默认字段：标题、描述、分类
        String[] defaultFields = {"title", "description", "category"};
        // 默认语言：中文、英文、韩文
        String[] defaultLocales = {"zh", "en", "ko"};
        
        return parseI18nObject(i18nJsonStr, defaultFields, defaultLocales);
    }
} 