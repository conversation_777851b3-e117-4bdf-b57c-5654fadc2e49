package com.mandong.api.module.erp.controller.admin.gamePackage;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.game.vo.GameVersionRespVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackageAddReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.PackagePageReqVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackGameApkVO;
import com.mandong.api.module.erp.controller.admin.gamePackage.vo.SdkPackResultRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkPackTaskResultDO;
import com.mandong.api.module.erp.service.gamePackage.PackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - ERP 渠道")
@RestController
@RequestMapping("/erp/package")
@Validated
public class PackageController {

    @Resource
    private PackageService packageService;


    @GetMapping("/page")
    @Operation(summary = "获得分包管理分页")
    @PreAuthorize("@ss.hasPermission('erp:package:query')")
    public CommonResult<PageResult<GameVersionRespVO>> getPackagePage(@Valid PackagePageReqVO reqVO) {
        return success(packageService.getPage(reqVO));
    }






    @PostMapping("/add")
    @Operation(summary = "添加分包任务")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<Long> addPackage(@Valid @RequestBody PackageAddReqVO packageAddReqVO) {
        return success(packageService.addPackage(packageAddReqVO));
    }



    @GetMapping("/delete")
    @Operation(summary = "删除分包任务")
    @PreAuthorize("@ss.hasPermission('erp:package:query')")
    public CommonResult<Integer> deletePackage(@Valid Long id) {
        return success(packageService.deletePackage(id));
    }


    @GetMapping("/getResult")
    @Operation(summary = "获取分包任务结果")
    public CommonResult<List<SdkPackResultRespVO>> getResult(@Valid Long id) {
        return success(packageService.getResult(id));
    }

}