package com.mandong.api.module.erp.controller.admin.pay;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.module.erp.controller.admin.pay.vo.*;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import com.mandong.api.module.erp.service.pay.PayConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点管理-支付配置")
@RestController
@RequestMapping("/site/pay-config")
@Validated
public class PayController {

    @Resource
    private PayConfigService payConfigService;

    @GetMapping("/page")
    @Operation(summary = "获得支付配置分页")
    @PreAuthorize("@ss.hasPermission('erp:pay-config:query')")
    public CommonResult<PageResult<PayConfigVO>> getPayConfigPage(@Valid PayConfigPageReqVO pageReqVO) {
        PageResult<PayConfigDO> pageResult = payConfigService.getPayConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PayConfigVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得支付配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:pay-config:query')")
    public CommonResult<PayConfigVO> getPayConfig(@RequestParam("id") Long id) {
        PayConfigDO payConfig = payConfigService.getPayConfigWithDetails(id);
        return success(BeanUtils.toBean(payConfig, PayConfigVO.class));
    }

    @PostMapping("/create")
    @Operation(summary = "创建支付配置")
    @PreAuthorize("@ss.hasPermission('erp:pay-config:create')")
    public CommonResult<Long> createPayConfig(@Valid @RequestBody PayConfigSaveReqVO createReqVO) {
        return success(payConfigService.createPayConfig(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新支付配置")
    @PreAuthorize("@ss.hasPermission('erp:pay-config:update')")
    public CommonResult<Boolean> updatePayConfig(@Valid @RequestBody PayConfigSaveReqVO updateReqVO) {
        payConfigService.updatePayConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @Operation(summary = "删除支付配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('erp:pay-config:delete')")
    public CommonResult<Boolean> deletePayConfig(@RequestParam("id") Long id) {
        payConfigService.deletePayConfig(id);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新支付配置状态")
    @PreAuthorize("@ss.hasPermission('erp:pay-config:update')")
    public CommonResult<Boolean> updatePayConfigStatus(@RequestBody PayConfigSaveReqVO payConfigSaveReqVO) {
        payConfigService.updatePayConfigStatus(payConfigSaveReqVO.getId(), payConfigSaveReqVO.getStatus());
        return success(true);
    }

    @GetMapping("/list-enabled")
    @Operation(summary = "获得启用的支付配置列表")
    public CommonResult<List<PayConfigVO>> getEnabledPayConfigList() {
        List<PayConfigDO> list = payConfigService.getEnabledPayConfigList();
        return success(BeanUtils.toBean(list, PayConfigVO.class));
    }

    @GetMapping("/list-for-payment")
    @Operation(summary = "获得支付页面可用的支付配置列表")
    @PermitAll
    public CommonResult<List<PayConfigForPaymentVO>> getPayConfigListForPayment() {
        List<PayConfigDO> list = payConfigService.getEnabledPayConfigListForPayment();
        return success(BeanUtils.toBean(list, PayConfigForPaymentVO.class));
    }

}
