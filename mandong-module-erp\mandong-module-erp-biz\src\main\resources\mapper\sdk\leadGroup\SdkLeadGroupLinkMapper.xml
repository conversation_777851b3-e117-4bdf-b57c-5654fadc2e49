<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.sdkMysql.leaderGroup.SdkLeadGroupLinkMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="getLeadGroupMonthlyStats"
            resultType="com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupMonthlyStatsVO">
        select FROM_UNIXTIME(o.payTime, '%Y-%m') month, p.productName productName, o.serverName serverName,
        sum(o.dealAmount) amount
        from qsdk_order o
        left join qsdk_product p on o.productId = p.id
        <where>
            o.payStatus = 1 and o.payTime between #{startTime} and #{endTime}

            AND EXISTS (
            SELECT 1
            FROM (
            -- 从带队关系表获取所有符合条件的产品、区服、渠道组合
            SELECT
            t.product_id AS productId,
            t.server_name AS serverName,
            t3.channel_code AS channelCode
            FROM
            qsdk_lead_group_link t
            LEFT JOIN qsdk_opt t2 ON t2.group_name = t.opt_group_name
            LEFT JOIN qsdk_opt_link t3 ON t3.link_id = t2.id AND t3.product_id = t.product_id
            WHERE
            t.link_id = #{id}
            AND (t.opt_group_name IS NULL OR t3.channel_code IS NOT NULL)
            ) AS filter_data
            WHERE
            (o.productId = filter_data.productId AND o.serverName = filter_data.serverName AND
            (filter_data.channelCode IS NULL OR o.channelCode = filter_data.channelCode))
            )
        </where>
        group by FROM_UNIXTIME(o.payTime, '%Y-%m'), p.productName, o.serverName

    </select>

    <select id="selectListByOptUserId"
            resultType="com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupOptVO">

        SELECT
            t.id,
            t.link_id,
            t.product_id,
            t.product_name,
            t.server_name,
            t.opt_group_name,
            t3.channel_code AS channelCode
        FROM
            qsdk_lead_group_link t
                INNER JOIN qsdk_lead_group t1 ON t1.id = t.link_id
                LEFT JOIN qsdk_opt t2 ON t2.group_name = t.opt_group_name
                LEFT JOIN qsdk_opt_link t3 ON t3.link_id = t2.id
                AND t3.product_id = t.product_id
        WHERE
            t1.uid = #{userId}
          AND (t.opt_group_name IS NULL OR t3.channel_code IS NOT NULL);
    </select>

    <select id="selectListByOpt" resultType="com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupOptVO">

        SELECT
            t.product_id AS productId,
            t.server_name AS serverName,
            t2.channel_code AS channelCode
        FROM
            qsdk_lead_group_link t
                LEFT JOIN qsdk_opt t1 ON (t1.group_name = t.opt_group_name)
                LEFT JOIN qsdk_opt_link t2 ON (t2.link_id = t1.id AND t2.product_id = t.product_id)
        WHERE
            t.link_id = #{id}
          AND (t.opt_group_name IS NULL OR t2.channel_code IS NOT NULL);
    </select>
</mapper>