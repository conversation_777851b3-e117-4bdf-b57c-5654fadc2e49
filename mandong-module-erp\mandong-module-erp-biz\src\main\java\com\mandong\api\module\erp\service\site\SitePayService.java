package com.mandong.api.module.erp.service.site;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.site.vo.*;
import jakarta.validation.Valid;

import java.util.List;

public interface SitePayService  {

    PageResult<SitePayPageRespVO> getPage(@Valid SitePayPageReqVO pageReqVO);
    List<SitePayPageRespVO> getGamePayList();

    Integer add(SitePayAddReqVO reqVO);
    Integer delete(Long id);
    SitePayDetailRespVO get(Long id);
    List<SitePaySkuRespVO> getSkuList(Long id);
    SitePayInfoRespVO getInfo(Long id);

    Integer edit(SitePayAddReqVO pageReqVO);

    Integer addSku(List<SitePayAddSkuReqVO> reqVO);

    Integer deleteSku(Long id);
    Integer updateSku(SitePayAddSkuReqVO reqVO);
    Integer deleteSku(List<Long> id);


}
