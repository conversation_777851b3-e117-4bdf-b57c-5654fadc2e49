package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 支付配置参数保存 Request VO")
@Data
public class PayConfigParamSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "配置编号", example = "1")
    private Long configId;

    @Schema(description = "参数键", requiredMode = Schema.RequiredMode.REQUIRED, example = "app_id")
    @NotEmpty(message = "参数键不能为空")
    private String paramKey;

    @Schema(description = "参数值", requiredMode = Schema.RequiredMode.REQUIRED, example = "2021001234567890")
    @NotEmpty(message = "参数值不能为空")
    private String paramValue;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "STRING")
    @NotEmpty(message = "参数类型不能为空")
    private String paramType;

    @Schema(description = "是否加密", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    @NotNull(message = "是否加密不能为空")
    private Boolean isEncrypted;

    @Schema(description = "参数描述", example = "应用ID")
    private String description;

}
