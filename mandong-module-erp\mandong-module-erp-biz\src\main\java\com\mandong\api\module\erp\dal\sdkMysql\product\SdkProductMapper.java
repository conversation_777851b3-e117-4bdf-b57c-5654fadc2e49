package com.mandong.api.module.erp.dal.sdkMysql.product;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.game.vo.GamePageReqVO;
import com.mandong.api.module.erp.controller.admin.game.vo.GamePageRespVO;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkProductMapper extends BaseMapperX<SdkProductDO> {


    default PageResult<GamePageRespVO> selectPage(GamePageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO,GamePageRespVO.class,new MPJLambdaWrapperX<SdkProductDO>()
                .selectAll(SdkProductDO.class)
                .inIfPresent(SdkProductDO::getId,pageReqVO.getId())
                .eqIfPresent(SdkProductDO::getPlatform,pageReqVO.getPlatform())
                .orderByDesc(SdkProductDO::getCreateTime)
        );
    }

}