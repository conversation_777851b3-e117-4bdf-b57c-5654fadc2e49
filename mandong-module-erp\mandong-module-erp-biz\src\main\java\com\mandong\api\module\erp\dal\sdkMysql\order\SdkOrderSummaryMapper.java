package com.mandong.api.module.erp.dal.sdkMysql.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.Calendar;
import java.util.Collections;

/**
 * SDK订单统计汇总 Mapper
 * 包含各种统计和汇总查询方法
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkOrderSummaryMapper extends BaseMapperX<SdkOrderDO> {

    /**
     * 获取每日订单支付摘要，以正确方式关联产品ID和区服
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = new MPJLambdaWrapperX<SdkOrderDO>();
        queryWrapper.selectSum(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPrice)
                   .selectCount(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPay)
                   .eq(SdkOrderDO::getPayStatus, 1)
                   .betweenIfPresent(SdkOrderDO::getPayTime, pageReqVO.getPayTime());

        queryWrapper.leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                   .select(SdkProductDO::getProductName)
                   .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode)
                   .select(SdkChannelDO::getChannelName)
                   .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                   .select(SdkPaysDO::getPayName);

        // 添加产品ID和区服组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        if (condition.getChannelCode() != null) {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName())
                                             .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        } else {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        }
                    } else {
                        if (condition.getChannelCode() != null) {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName())
                                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        } else {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        }
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                queryWrapper.eq(SdkOrderDO::getServerName, pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                queryWrapper.in(SdkOrderDO::getServerName, pageReqVO.getServers());
            }
        }

        return selectJoinOne(SdkOrderSummaryRespVO.class, queryWrapper);
    }

    /**
     * 保留原来的方法，但内部调用新方法
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderPaySummaryByDay(pageReqVO, null);
    }

    /**
     * 获取每周订单支付摘要，以正确方式关联产品ID和区服
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 计算周时间范围
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = new MPJLambdaWrapperX<SdkOrderDO>();
        queryWrapper.selectSum(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getWeekPrice)
                   .selectCount(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getWeekPay)
                   .between(SdkOrderDO::getPayTime, startTime, endTime)
                   .eq(SdkOrderDO::getPayStatus, 1);

        queryWrapper.leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                   .select(SdkProductDO::getProductName)
                   .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode)
                   .select(SdkChannelDO::getChannelName)
                   .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                   .select(SdkPaysDO::getPayName);

        // 添加产品ID和区服组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        if (condition.getChannelCode() != null) {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName())
                                             .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        } else {
                            wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                             .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        }
                    } else {
                        if (condition.getChannelCode() != null) {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName())
                                            .eq(SdkOrderDO::getChannelCode, condition.getChannelCode()));
                        } else {
                            wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                            .eq(SdkOrderDO::getServerName, condition.getServerName()));
                        }
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                queryWrapper.eq(SdkOrderDO::getServerName, pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                queryWrapper.in(SdkOrderDO::getServerName, pageReqVO.getServers());
            }
        }

        return selectJoinOne(SdkOrderSummaryRespVO.class, queryWrapper);
    }

    /**
     * 保留原来的方法，但内部调用新方法
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderPaySummaryByWeek(pageReqVO, null);
    }

    /**
     * 获取每月订单支付摘要，以正确方式关联产品ID和区服
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByMonth(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 设置本月的时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkOrderDO> queryWrapper = new MPJLambdaWrapperX<SdkOrderDO>();
        queryWrapper.selectSum(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPrice)
                   .selectCount(SdkOrderDO::getDealAmount, SdkOrderSummaryRespVO::getTodayPay)
                   .between(SdkOrderDO::getPayTime, startTime, endTime)
                   .eq(SdkOrderDO::getPayStatus, 1);

        queryWrapper.leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkOrderDO::getProductId)
                   .select(SdkProductDO::getProductName)
                   .leftJoin(SdkChannelDO.class, SdkChannelDO::getId, SdkOrderDO::getChannelCode)
                   .select(SdkChannelDO::getChannelName)
                   .leftJoin(SdkPaysDO.class, SdkPaysDO::getId, SdkOrderDO::getPayType)
                   .select(SdkPaysDO::getPayName);

        // 添加产品ID和区服组合条件
        if (CollectionUtils.isNotEmpty(conditions)) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                         .eq(SdkOrderDO::getServerName, condition.getServerName()));
                    } else {
                        wrapper.or(w -> w.eq(SdkOrderDO::getProductId, condition.getProductId())
                                        .eq(SdkOrderDO::getServerName, condition.getServerName()));
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                queryWrapper.eq(SdkOrderDO::getServerName, pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                queryWrapper.in(SdkOrderDO::getServerName, pageReqVO.getServers());
            }
        }

        return selectJoinOne(SdkOrderSummaryRespVO.class, queryWrapper);
    }

    /**
     * 保留原来的方法，但内部调用新方法
     */
    default SdkOrderSummaryRespVO getOrderPaySummaryByMonth(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderPaySummaryByMonth(pageReqVO, null);
    }

    /**
     * 获取每日付费用户统计，支持按照产品ID和区服组合进行交叉查询
     */
    SdkOrderSummaryRespVO getOrderUserPaySummaryByDay(@Param("payTime") Long[] payTime,
                                                      @Param("productId") List<Long> productId,
                                                      @Param("serverNames") List<String> serverNames,
                                                      @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 统计一周内的付费用户数，支持按照产品ID和区服组合进行交叉查询
     */
    Long countWeekPayUsers(@Param("startTime") Long startTime,
                           @Param("endTime") Long endTime,
                           @Param("productId") List<Long> productId,
                           @Param("serverNames") List<String> serverNames,
                           @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 按天统计交易金额，支持按照产品ID和区服组合进行交叉查询
     */
    List<SdkOrderSummaryMonthRespVO> getDailyTradeAmount(@Param("startTime") Long startTime,
                                                         @Param("endTime") Long endTime,
                                                         @Param("productId") List<Long> productId,
                                                         @Param("serverNames") List<String> serverNames,
                                                         @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 按天统计订单数，支持按照产品ID和区服组合进行交叉查询
     */
    List<SdkOrderSummaryMonthRespVO> getDailyPay(@Param("startTime") Long startTime,
                                                 @Param("endTime") Long endTime,
                                                 @Param("productId") List<Long> productId,
                                                 @Param("serverNames") List<String> serverNames,
                                                 @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 按天统计付费用户数，支持按照产品ID和区服组合进行交叉查询
     */
    List<SdkOrderSummaryMonthRespVO> getDailyPayUsers(@Param("startTime") Long startTime,
                                                      @Param("endTime") Long endTime,
                                                      @Param("productId") List<Long> productId,
                                                      @Param("serverNames") List<String> serverNames,
                                                      @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 根据产品ID查询服务器列表
     */
    List<String> selectSeverByProductId(@Param("productId") List<Long> productId);

    /**
     * 重载方法，支持原有调用
     */
    default SdkOrderSummaryRespVO getOrderUserPaySummaryByDay(Long[] payTime, List<Long> productId) {
        return getOrderUserPaySummaryByDay(payTime, productId, null, null);
    }

    /**
     * 重载方法，支持原有调用
     */
    default Long countWeekPayUsers(Long startTime, Long endTime, List<Long> productId) {
        return countWeekPayUsers(startTime, endTime, productId, null, null);
    }

    /**
     * 获取用户付费统计信息（基于条件列表的增强版）
     */
    default SdkOrderSummaryRespVO getOrderUserPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        List<Long> productIds = null;
        List<String> serverNames = null;

        // 如果没有条件列表，则使用请求中的产品ID
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();

            // 提取服务器名称
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                serverNames = Collections.singletonList(pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                serverNames = pageReqVO.getServers();
            }

            return getOrderUserPaySummaryByDay(pageReqVO.getPayTime(), productIds, serverNames, null);
        } else {
            return getOrderUserPaySummaryByDay(pageReqVO.getPayTime(), null, null, conditions);
        }
    }

    /**
     * 不带条件的方法版本
     */
    default SdkOrderSummaryRespVO getOrderUserPaySummaryByDay(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderUserPaySummaryByDay(pageReqVO, null);
    }

    /**
     * 获取每周付费用户统计，以正确方式关联产品ID和区服
     */
    default SdkOrderSummaryRespVO getOrderUserPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        List<Long> productIds = null;
        List<String> serverNames = null;

        // 如果没有条件列表，则使用请求中的产品ID
        if (CollectionUtils.isEmpty(conditions)) {
            productIds = pageReqVO.getProductId();

            // 提取服务器名称
            if (pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty()) {
                serverNames = Collections.singletonList(pageReqVO.getServerName());
            } else if (pageReqVO.getServers() != null && !pageReqVO.getServers().isEmpty()) {
                serverNames = pageReqVO.getServers();
            }

            Long weekPayUsers = countWeekPayUsers(startTime, endTime, productIds, serverNames, null);
            SdkOrderSummaryRespVO result = new SdkOrderSummaryRespVO();
            result.setWeekPayUser(weekPayUsers);
            return result;
        } else {
            Long weekPayUsers = countWeekPayUsers(startTime, endTime, null, null, conditions);
            SdkOrderSummaryRespVO result = new SdkOrderSummaryRespVO();
            result.setWeekPayUser(weekPayUsers);
            return result;
        }
    }

    /**
     * 不带条件的方法版本
     */
    default SdkOrderSummaryRespVO getOrderUserPaySummaryByWeek(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderUserPaySummaryByWeek(pageReqVO, null);
    }

    /**
     * 获取按月交易金额统计
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyTradeAmount(SdkOrderSummaryReqVO pageReqVO) {
        return getMonthlyTradeAmount(pageReqVO, null);
    }

    /**
     * 获取按月交易金额统计（带条件）
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyTradeAmount(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        if (conditions == null || conditions.isEmpty()) {
            // 查询月活跃用户数
            return getDailyTradeAmount(startOfMonth, endOfMonth, pageReqVO.getProductId(), null, null);
        }
        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;

        // 先检查用户的请求参数中是否指定了查询条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {

            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();


            // 只有产品ID的情况，需要用权限条件中的服务器名称
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : conditions) {
                    if (pageReqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }

            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && conditions != null && !conditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : conditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) &&
                                requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = conditions;
        }

        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            return Collections.emptyList();
        }
        return getDailyTradeAmount(startOfMonth, endOfMonth, null, null, conditions);
    }

    /**
     * 获取按月订单数统计
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyPay(SdkOrderSummaryReqVO pageReqVO) {
        return getMonthlyPay(pageReqVO, null);
    }

    /**
     * 获取按月订单数统计（带条件）
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyPay(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        if (conditions == null || conditions.isEmpty()) {
            return getDailyPay(startOfMonth, endOfMonth, pageReqVO.getProductId(), null, null);
        }
        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;

        // 先检查用户的请求参数中是否指定了查询条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {

            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();


            // 只有产品ID的情况，需要用权限条件中的服务器名称
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : conditions) {
                    if (pageReqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }

            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && conditions != null && !conditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : conditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) &&
                                requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = conditions;
        }

        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            return Collections.emptyList();
        }
        return getDailyPay(startOfMonth, endOfMonth, null, null, conditions);
    }

    /**
     * 获取按月付费用户数统计
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyPayUsers(SdkOrderSummaryReqVO pageReqVO) {
        return getMonthlyPayUsers(pageReqVO, null);
    }

    /**
     * 获取按月付费用户数统计（带条件）
     */
    default List<SdkOrderSummaryMonthRespVO> getMonthlyPayUsers(SdkOrderSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 计算月份的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        Long startOfMonth, endOfMonth;

        if (payTime != null && payTime.length == 2) {
            // 使用前端传入的时间
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startOfMonth = calendar.getTimeInMillis() / 1000;

        // 设置为本月最后一天
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        endOfMonth = calendar.getTimeInMillis() / 1000;

        if (conditions == null || conditions.isEmpty()) {
            return getDailyPayUsers(startOfMonth, endOfMonth, pageReqVO.getProductId(), null, null);
        }
        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;

        // 先检查用户的请求参数中是否指定了查询条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {

            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();


            // 只有产品ID的情况，需要用权限条件中的服务器名称
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : conditions) {
                    if (pageReqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }

            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && conditions != null && !conditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : conditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) &&
                                requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = conditions;
        }

        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            return Collections.emptyList();
        }
        return getDailyPayUsers(startOfMonth, endOfMonth, null, null, conditions);
    }
}
