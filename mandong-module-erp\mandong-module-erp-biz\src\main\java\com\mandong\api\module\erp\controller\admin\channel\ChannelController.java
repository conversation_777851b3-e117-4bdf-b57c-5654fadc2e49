package com.mandong.api.module.erp.controller.admin.channel;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.ChannelGetListRespVO;
import com.mandong.api.module.erp.controller.admin.channel.vo.channel.SdkChannelVO;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.service.channel.ChannelService;
import com.mandong.api.module.erp.service.product.ErpProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - ERP 渠道")
@RestController
@RequestMapping("/erp/channel")
@Validated
public class ChannelController {

    @Resource
    private ChannelService channelService;


    @GetMapping("/page")
    @Operation(summary = "获得渠道分页")
    @PreAuthorize("@ss.hasPermission('erp:channel:query')")
    public CommonResult<PageResult<SdkChannelVO>> getChannelPage(@Valid ChannelGetListRespVO channelGetListRespVO) {
        return success(channelService.getChannelPage(channelGetListRespVO));
    }




    @GetMapping("/list")
    @Operation(summary = "获得渠道")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<PageResult<SdkChannelVO>> getChannel(@Valid ChannelGetListRespVO channelGetListRespVO) {
        return success(channelService.getChannel(channelGetListRespVO));
    }

    @PostMapping("/add")
    @Operation(summary = "添加渠道")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<Long> addChannel(@Valid @RequestBody SdkChannelVO sdkChannelVO) {
        return success(channelService.addChannel(sdkChannelVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得渠道")
    @PreAuthorize("@ss.hasPermission('erp:channel:query')")
    public CommonResult<SdkChannelDO> getChannel(@Valid Long id) {
        return success(channelService.getChannel(id));
    }

    @GetMapping("/delete")
    @Operation(summary = "获得渠道")
    @PreAuthorize("@ss.hasPermission('erp:channel:query')")
    public CommonResult<Integer> deleteChannel(@Valid Long id) {
        return success(channelService.deleteChannel(id));
    }
    @PostMapping("/edit")
    @Operation(summary = "添加渠道")
    @PreAuthorize("@ss.hasPermission('erp:channel:edit')")
    public CommonResult<Long> editChannel(@Valid @RequestBody SdkChannelVO sdkChannelVO) {
        return success(channelService.editChannel(sdkChannelVO));
    }


}