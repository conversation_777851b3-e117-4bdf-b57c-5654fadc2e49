package com.mandong.api.module.erp.dal.mysql.pay;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.pay.SdkPays;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("sdkDB")
public interface SdkPaysMapper extends BaseMapperX<SdkPays> {
}