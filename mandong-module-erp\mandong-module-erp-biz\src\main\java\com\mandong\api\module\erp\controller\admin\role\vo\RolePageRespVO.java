package com.mandong.api.module.erp.controller.admin.role.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mandong.api.framework.excel.core.annotations.DictFormat;
import com.mandong.api.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RolePageRespVO {

    @Schema(description = "用户id", example = "22173")
    @ExcelProperty("用户id")
    private Long uid;

    @Schema(description = "用户名", example = "22173")
    @ExcelProperty("用户名")
    private String userName;

    @Schema(description = "游戏名", example = "22173")
    @ExcelProperty("游戏名")
    private String productName;

    @Schema(description = "渠道", example = "22173")
    @ExcelProperty("渠道")
    private String channelName;

    @Schema(description = "运营人员", example = "22173")
    @ExcelProperty("运营人员")
    private String optName;


    @Schema(description = "区服", example = "22173")
    @ExcelProperty("区服")
    private String serverName;

    @Schema(description = "角色名", example = "22173")
    @ExcelProperty("角色名")
    private String gameRoleName;

    @Schema(description = "角色id", example = "22173")
    @ExcelProperty("角色id")
    private String gameRoleId;

    @Schema(description = "角色等级", example = "22173")
    @ExcelProperty("角色等级")
    private String gameRoleLevel;


    @Schema(description = "最后登录时间" )
    @ExcelProperty("最后登录时间")
    private Long lastLoginTime;

    @Schema(description = "总付费额")
    private Float payAmount =0F;

    @Schema(description = "付款次数")
    private Long payNum;

    @Schema(description = "设备id")
    private String deviceId;



}
