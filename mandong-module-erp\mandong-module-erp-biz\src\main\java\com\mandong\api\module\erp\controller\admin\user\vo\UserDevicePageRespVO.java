package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户激活 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserDevicePageRespVO {

    /**
     * 用户id
     */
    @Schema(description = "用户id", example = "22173")
    private Integer uid;

    /**
     * 设备id
     */
    @Schema(description = "设备id", example = "22173")
    private String deviceId;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间", example = "22173")
    private Long activeTime;

    /**
     * 产品
     */
    @Schema(description = "产品", example = "22173")
    private String productName;

    @Schema(description = "运营人员", example = "22173")
    private String channelName;
}
