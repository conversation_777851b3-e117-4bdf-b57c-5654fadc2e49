package com.mandong.api.module.erp.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户统计 Response VO
 */
@Data
@ExcelIgnoreUnannotated
public class SdkUserSummaryRespVO {

    @Schema(description = "今日注册数")
    private Long todayRegistration;
    @Schema(description = "当周注册数")
    private Long weekRegistration;


    
    @Schema(description = "日活跃用户数")
    private Long todayActiveUsers;
    
    @Schema(description = "周活跃用户数")
    private Long weekActiveUsers;
    
    @Schema(description = "月活跃用户数")
    private Long monthActiveUsers;

    @Schema(description = "月注册数")
    private List<SdkOrderSummaryMonthRespVO> monthlyRegistration;

}
