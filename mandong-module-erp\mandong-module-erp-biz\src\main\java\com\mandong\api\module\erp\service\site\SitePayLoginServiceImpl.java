package com.mandong.api.module.erp.service.site;

import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.module.erp.controller.admin.site.SitePayLoginController;
import com.mandong.api.module.erp.controller.admin.site.vo.SitePayLoginVO;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLogin;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLoginConfig;
import com.mandong.api.module.erp.dal.mysql.site.SitePayLoginConfigMapper;
import com.mandong.api.module.erp.dal.mysql.site.SitePayLoginMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
@Service
public class SitePayLoginServiceImpl implements SitePayLoginService {
    @Resource
    SitePayLoginMapper sitePayLoginMapper;
    @Resource
    SitePayLoginConfigMapper sitePayLoginConfigMapper;
    @Override
    public List<SitePayLogin> getList(Long sitePayId) {
        return sitePayLoginMapper.selectList(SitePayLogin::getSitePayId, sitePayId);
    }

    @Override
    public SitePayLoginVO get(Long id) {
        return sitePayLoginMapper.get(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(SitePayLoginVO reqVO) {
        SitePayLogin sitePayLogin = BeanUtils.toBean(reqVO, SitePayLogin.class);
        SitePayLoginConfig sitePayLoginConfig = BeanUtils.toBean(reqVO, SitePayLoginConfig.class);
        sitePayLoginMapper.insertOrUpdate(sitePayLogin);
        sitePayLoginConfigMapper.insertOrUpdate(sitePayLoginConfig);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer delete(Long id) {
        SitePayLogin sitePayLogin = sitePayLoginMapper.selectOne(SitePayLogin::getId, id);
        sitePayLoginMapper.deleteById(id);

        sitePayLoginConfigMapper.delete(new LambdaQueryWrapperX<SitePayLoginConfig>().eq(SitePayLoginConfig::getSitePayId, sitePayLogin.getSitePayId()).eq(SitePayLoginConfig::getType, sitePayLogin.getType()));


        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(SitePayLoginVO reqVO) {
        SitePayLogin sitePayLogin = BeanUtils.toBean(reqVO, SitePayLogin.class);
        SitePayLoginConfig sitePayLoginConfig = BeanUtils.toBean(reqVO, SitePayLoginConfig.class);
        sitePayLoginMapper.insertOrUpdate(sitePayLogin);
        sitePayLoginConfigMapper.insertOrUpdate(sitePayLoginConfig);
        return 0;
    }
}
