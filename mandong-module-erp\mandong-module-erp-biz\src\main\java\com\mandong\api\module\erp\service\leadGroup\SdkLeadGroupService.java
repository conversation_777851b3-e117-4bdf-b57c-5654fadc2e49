package com.mandong.api.module.erp.service.leadGroup;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import jakarta.validation.Valid;

import java.util.List;

public interface SdkLeadGroupService {
    PageResult<LeadGroupRespVO> getLeadGroupPage(LeadGroupPageReqVO pageReqVO);
    PageResult<LeadGroupLinksPageRespVO> getLeadGroupLinksPage(LeadGroupLinksPageReqVO pageReqVO);

    int delete(long id);
    int deleteGroupLinks(long id);
    int add(LeadGroupAddReqVO reqVO);
    int createGroupLinks(LeadGroupLinkAddReqVO reqVO);

    List<SdkLeadGroupLinkDO> getLeadGroupLinks(long id);

    List<LeadGroupMonthlyStatsVO> getLeadGroupMonthlyStats(Long id ,Long  startTime,Long  endTime);
}
