package com.mandong.api.module.erp.service.pay;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigPageReqVO;
import com.mandong.api.module.erp.controller.admin.pay.vo.PayConfigSaveReqVO;
import com.mandong.api.module.erp.dal.dataobject.pay.PayConfigDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 支付配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PayConfigService {

    /**
     * 创建支付配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPayConfig(@Valid PayConfigSaveReqVO createReqVO);

    /**
     * 更新支付配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePayConfig(@Valid PayConfigSaveReqVO updateReqVO);

    /**
     * 删除支付配置
     *
     * @param id 编号
     */
    void deletePayConfig(Long id);

    /**
     * 获得支付配置
     *
     * @param id 编号
     * @return 支付配置
     */
    PayConfigDO getPayConfig(Long id);

    /**
     * 获得支付配置（包含参数和方法）
     *
     * @param id 编号
     * @return 支付配置
     */
    PayConfigDO getPayConfigWithDetails(Long id);

    /**
     * 获得支付配置分页
     *
     * @param pageReqVO 分页查询
     * @return 支付配置分页
     */
    PageResult<PayConfigDO> getPayConfigPage(PayConfigPageReqVO pageReqVO);

    /**
     * 更新支付配置状态
     *
     * @param id 编号
     * @param status 状态
     */
    void updatePayConfigStatus(Long id, Integer status);

    /**
     * 获得启用的支付配置列表
     *
     * @return 支付配置列表
     */
    List<PayConfigDO> getEnabledPayConfigList();

    /**
     * 获得启用的支付配置列表（包含启用的支付方式）
     * 用于支付页面展示给用户选择
     *
     * @return 支付配置列表
     */
    List<PayConfigDO> getEnabledPayConfigListForPayment();

}
