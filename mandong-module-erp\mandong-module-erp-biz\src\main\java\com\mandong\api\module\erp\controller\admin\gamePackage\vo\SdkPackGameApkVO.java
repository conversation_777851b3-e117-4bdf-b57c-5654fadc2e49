package com.mandong.api.module.erp.controller.admin.gamePackage.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class SdkPackGameApkVO {

    private Integer id;


    private String icon;

    private String appName;
    private String productName;


    private String initImage;

    private Integer productId;

    private Integer versionNo;

    private String versionName;

    private String note;

    private String apkUrl;

    private Integer updateTime;

    private String size;

    private String Package;

    /**
    * 1执行成功 2等待 3正在执行 4失败
    */
    private Integer status;

    /**
    * 1 使用Quick
    */
    private Integer useQuick;

    private String messageTips;

    private String taskInfo;
}