package com.mandong.api.module.erp.service.opt;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.opt.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptLinkMapper;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.*;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;

@Slf4j
@Service
@Validated
public class SdkOptServiceImpl implements SdkOptService {
    @Resource
    SdkOptMapper sdkOptMapper;

    @Resource
    SdkOptLinkMapper sdkOptLinkMapper;

    @Override
    public PageResult<OptRespVO> getOptPage(OptPageReqVO pageReqVO) {
        List<String> adminRole = Arrays.asList("1","2","3","4");
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 如果不是管理员角色 ，查询他自己
        if(!adminRole.contains(roleId)){
            pageReqVO.setUid(userId);
        }

        return sdkOptMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<OptLinksPageRespVO> getOptLinksPage(OptLinksPageReqVO pageReqVO) {
        return sdkOptLinkMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SdkOptLinkDO> getOptLinks(long id) {

        return sdkOptLinkMapper.selectList(SdkOptLinkDO::getLinkId, id);
    }

    @Override
    public List<OptMonthlyStatsVO> getOptMonthlyStats(Long id,Long  stime,Long  etime) {
        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 计算上个月和上上个月的日期
        LocalDate twoMonthsAgo = now.minusMonths(2);

        // 计算开始时间和结束时间
        LocalDateTime startOfMonthTwoMonthsAgo = LocalDateTime.of(twoMonthsAgo.withDayOfMonth(1), LocalTime.MIN);
        LocalDateTime endOfDayNow = LocalDateTime.of(now, LocalTime.MAX);

        // 将LocalDateTime转换为Instant以获取时间戳
        Instant startInstantTwoMonthsAgo = startOfMonthTwoMonthsAgo.atZone(ZoneId.systemDefault()).toInstant();
        Instant endInstantNow = endOfDayNow.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为秒级时间戳
        long startTime = startInstantTwoMonthsAgo.getEpochSecond();
        long endTime = endInstantNow.getEpochSecond();

        if (stime != null) {
            startTime = stime;
            endTime = etime;
        }
        return sdkOptLinkMapper.getOptMonthlyStats(id,startTime,endTime);
    }

    @Override
    public int delete(long id) {

        return sdkOptMapper.delete(SdkOptDO::getId,id);
    }

    @Override
    public int deleteGroupLinks(long id) {
        return sdkOptLinkMapper.delete(SdkOptLinkDO::getId,id);
    }

    @Override
    public int add(OptAddReqVO reqVO) {
        SdkOptDO bean = BeanUtils.toBean(reqVO, SdkOptDO.class);


        return  sdkOptMapper.insert(bean);
    }

    @Override
    public int createGroupLinks(OptLinkAddReqVO reqVO) {

        SdkOptDO sdkOptDO = sdkOptMapper.selectById(reqVO.getLinkId());
        if (sdkOptDO == null) {
            throw exception(OPT_NOT_EXISTS);
        }
        SdkOptLinkDO sdkOptLinkDO = sdkOptLinkMapper.selectOne(SdkOptLinkDO::getProductId, reqVO.getProductId(), SdkOptLinkDO::getChannelCode, reqVO.getChannelCode());
        if (sdkOptLinkDO != null) {
            throw exception(OPT_LINKS_EXISTS_SERVER);
        }


        return sdkOptLinkMapper.insert(BeanUtils.toBean(reqVO, SdkOptLinkDO.class));
    }
}
