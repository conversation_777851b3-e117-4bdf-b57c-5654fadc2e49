package com.mandong.api.module.erp.controller.admin.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 支付方式新增/修改 Request VO")
@Data
public class PayMethodSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    private String type;
    @Schema(description = "支付方式编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "alipay")
    @NotEmpty(message = "支付方式编码不能为空")
    private String code;


    @Schema(description = "支付方式名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "支付宝")
    @NotEmpty(message = "支付方式名称不能为空")
    private String name;

    @Schema(description = "支付提供商", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAYERMAX")
    @NotEmpty(message = "支付提供商不能为空")
    private String provider;

    @Schema(description = "图标地址", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "描述", example = "支付宝支付方式")
    private String description;

    @Schema(description = "排序", example = "1")
    private Integer sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;


}
